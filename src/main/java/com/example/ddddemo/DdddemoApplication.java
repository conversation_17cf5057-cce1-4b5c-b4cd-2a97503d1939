package com.example.ddddemo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main Spring Boot Application for the DDD Aviation Pricing System.
 *
 * This application demonstrates comprehensive Domain Driven Design (DDD) patterns
 * in the context of a civil aviation ticket pricing rule management system.
 *
 * Key DDD concepts implemented:
 * - Layered Architecture (Domain, Application, Infrastructure, Presentation)
 * - Bounded Contexts (Flight Management, Pricing, Customer Management)
 * - Tactical DDD Patterns (Entities, Value Objects, Aggregates, Domain Services)
 * - Strategic DDD Patterns (Context Mapping, Anti-Corruption Layer)
 * - CQRS (Command Query Responsibility Segregation)
 * - Event Sourcing and Domain Events
 *
 * Learning Objectives:
 * 1. Understand how DDD solves complex business problems
 * 2. Learn proper aggregate design and boundaries
 * 3. Implement domain events for loose coupling
 * 4. Apply specification pattern for business rules
 * 5. Use repository pattern with proper abstractions
 * 6. Demonstrate clean code principles (SOLID, DRY)
 *
 * Technical Stack:
 * - Spring Boot 3.x with Java 21
 * - Spring Data JPA for persistence
 * - H2 in-memory database for learning
 * - Spring Web for REST API
 * - Comprehensive testing with JUnit 5
 */
@SpringBootApplication
@EnableTransactionManagement
public class DdddemoApplication {

	public static void main(String[] args) {
		SpringApplication.run(DdddemoApplication.class, args);
	}

}
