package com.example.ddddemo.domain.pricing.specifications;

import com.example.ddddemo.domain.shared.Specification;

/**
 * Composite Specification that determines if premium pricing should be applied.
 * 
 * This specification demonstrates the power of the Specification pattern by
 * combining multiple business rules using logical operators. Premium pricing
 * is applied when a booking meets certain high-demand criteria.
 * 
 * Business Rule: Premium pricing applies when:
 * (Flight is on a popular route OR flight is on weekend) AND flight has high demand
 * 
 * This demonstrates:
 * - Composition of specifications using logical operators
 * - Complex business rule expression
 * - Reusability of existing specifications
 * - Clear separation of concerns
 * 
 * The composite specification is built from:
 * - IsPopularRouteSpecification
 * - IsWeekendFlightSpecification  
 * - IsHighDemandFlightSpecification
 * 
 * Usage Example:
 * ```java
 * Specification<BookingContext> premiumPricingSpec = new PremiumPricingSpecification();
 * if (premiumPricingSpec.isSatisfiedBy(bookingContext)) {
 *     // Apply premium pricing surcharge
 * }
 * ```
 */
public class PremiumPricingSpecification implements Specification<BookingContext> {
    
    /**
     * Specification for popular routes.
     */
    private final Specification<BookingContext> popularRouteSpec;
    
    /**
     * Specification for weekend flights.
     */
    private final Specification<BookingContext> weekendFlightSpec;
    
    /**
     * Specification for high demand flights.
     */
    private final Specification<BookingContext> highDemandSpec;
    
    /**
     * The composed specification that represents the premium pricing rule.
     */
    private final Specification<BookingContext> composedSpecification;
    
    /**
     * Creates a new PremiumPricingSpecification with default component specifications.
     */
    public PremiumPricingSpecification() {
        this.popularRouteSpec = new PopularRouteContextSpecification();
        this.weekendFlightSpec = new WeekendFlightContextSpecification();
        this.highDemandSpec = new HighDemandFlightSpecification();
        
        // Build the composite specification:
        // (Popular Route OR Weekend Flight) AND High Demand
        this.composedSpecification = popularRouteSpec
            .or(weekendFlightSpec)
            .and(highDemandSpec);
    }
    
    /**
     * Creates a new PremiumPricingSpecification with custom component specifications.
     * This constructor allows for dependency injection and testing with mock specifications.
     * 
     * @param popularRouteSpec Specification for popular routes
     * @param weekendFlightSpec Specification for weekend flights
     * @param highDemandSpec Specification for high demand flights
     */
    public PremiumPricingSpecification(Specification<BookingContext> popularRouteSpec,
                                     Specification<BookingContext> weekendFlightSpec,
                                     Specification<BookingContext> highDemandSpec) {
        this.popularRouteSpec = popularRouteSpec;
        this.weekendFlightSpec = weekendFlightSpec;
        this.highDemandSpec = highDemandSpec;
        
        this.composedSpecification = popularRouteSpec
            .or(weekendFlightSpec)
            .and(highDemandSpec);
    }
    
    @Override
    public boolean isSatisfiedBy(BookingContext context) {
        return composedSpecification.isSatisfiedBy(context);
    }
    
    /**
     * Checks if the booking qualifies for the route-based component of premium pricing.
     * 
     * @param context The booking context
     * @return true if the route is popular
     */
    public boolean qualifiesForRoutePremium(BookingContext context) {
        return popularRouteSpec.isSatisfiedBy(context);
    }
    
    /**
     * Checks if the booking qualifies for the time-based component of premium pricing.
     * 
     * @param context The booking context
     * @return true if the flight is on a weekend
     */
    public boolean qualifiesForTimePremium(BookingContext context) {
        return weekendFlightSpec.isSatisfiedBy(context);
    }
    
    /**
     * Checks if the booking qualifies for the demand-based component of premium pricing.
     * 
     * @param context The booking context
     * @return true if the flight has high demand
     */
    public boolean qualifiesForDemandPremium(BookingContext context) {
        return highDemandSpec.isSatisfiedBy(context);
    }
    
    /**
     * Gets a detailed breakdown of which premium pricing criteria are met.
     * This is useful for transparency and debugging.
     * 
     * @param context The booking context
     * @return Premium pricing breakdown
     */
    public PremiumPricingBreakdown getBreakdown(BookingContext context) {
        return new PremiumPricingBreakdown(
            qualifiesForRoutePremium(context),
            qualifiesForTimePremium(context),
            qualifiesForDemandPremium(context),
            isSatisfiedBy(context)
        );
    }
    
    /**
     * Calculates the suggested premium surcharge percentage.
     * This demonstrates how composite specifications can provide additional business logic.
     * 
     * @param context The booking context
     * @return Suggested premium surcharge percentage
     */
    public double calculatePremiumSurcharge(BookingContext context) {
        if (!isSatisfiedBy(context)) {
            return 0.0;
        }
        
        double surcharge = 0.0;
        
        // Base premium for high demand
        if (qualifiesForDemandPremium(context)) {
            surcharge += 15.0; // 15% base premium for high demand
        }
        
        // Additional premium for popular route
        if (qualifiesForRoutePremium(context)) {
            surcharge += 10.0; // Additional 10% for popular route
        }
        
        // Additional premium for weekend flight
        if (qualifiesForTimePremium(context)) {
            surcharge += 8.0; // Additional 8% for weekend flight
        }
        
        // Cap the maximum premium surcharge
        return Math.min(surcharge, 35.0); // Maximum 35% premium surcharge
    }
    
    /**
     * Value object that provides a breakdown of premium pricing criteria.
     */
    public static class PremiumPricingBreakdown {
        private final boolean routePremium;
        private final boolean timePremium;
        private final boolean demandPremium;
        private final boolean overallPremium;
        
        public PremiumPricingBreakdown(boolean routePremium, boolean timePremium, 
                                     boolean demandPremium, boolean overallPremium) {
            this.routePremium = routePremium;
            this.timePremium = timePremium;
            this.demandPremium = demandPremium;
            this.overallPremium = overallPremium;
        }
        
        public boolean hasRoutePremium() { return routePremium; }
        public boolean hasTimePremium() { return timePremium; }
        public boolean hasDemandPremium() { return demandPremium; }
        public boolean hasOverallPremium() { return overallPremium; }
        
        @Override
        public String toString() {
            return String.format("PremiumPricingBreakdown{route=%s, time=%s, demand=%s, overall=%s}",
                               routePremium, timePremium, demandPremium, overallPremium);
        }
    }
    
    /**
     * Adapter specification that applies popular route logic to BookingContext.
     */
    private static class PopularRouteContextSpecification implements Specification<BookingContext> {
        private final IsPopularRouteSpecification routeSpec = new IsPopularRouteSpecification();
        
        @Override
        public boolean isSatisfiedBy(BookingContext context) {
            return context != null && routeSpec.isSatisfiedBy(context.getFlight().getRoute());
        }
    }
    
    /**
     * Adapter specification that applies weekend flight logic to BookingContext.
     */
    private static class WeekendFlightContextSpecification implements Specification<BookingContext> {
        private final IsWeekendFlightSpecification weekendSpec = new IsWeekendFlightSpecification();
        
        @Override
        public boolean isSatisfiedBy(BookingContext context) {
            return context != null && weekendSpec.isSatisfiedBy(context.getFlight());
        }
    }
    
    /**
     * Specification for high demand flights based on load factor.
     */
    private static class HighDemandFlightSpecification implements Specification<BookingContext> {
        private static final double HIGH_DEMAND_THRESHOLD = 80.0;
        
        @Override
        public boolean isSatisfiedBy(BookingContext context) {
            return context != null && context.getFlightLoadFactor() >= HIGH_DEMAND_THRESHOLD;
        }
    }
}
