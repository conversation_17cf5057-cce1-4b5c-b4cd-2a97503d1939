package com.example.ddddemo.domain.pricing.specifications;

import com.example.ddddemo.domain.shared.Specification;
import com.example.ddddemo.domain.flight.Flight;
import java.time.DayOfWeek;
import java.time.LocalDateTime;

/**
 * Specification that determines if a flight departs on a weekend.
 * 
 * This specification encapsulates the business rule for weekend flights,
 * which typically have higher demand and therefore higher prices.
 * Weekend flights are defined as flights departing on Friday evening,
 * Saturday, or Sunday.
 * 
 * Business Rule: A flight is considered a weekend flight if:
 * 1. It departs on Saturday or Sunday, OR
 * 2. It departs on Friday after 6:00 PM
 * 
 * This demonstrates:
 * - Simple specification implementation
 * - Time-based business rules
 * - Clear encapsulation of weekend logic
 * 
 * Usage Example:
 * ```java
 * Specification<Flight> weekendSpec = new IsWeekendFlightSpecification();
 * if (weekendSpec.isSatisfiedBy(flight)) {
 *     // Apply weekend surcharge
 * }
 * ```
 */
public class IsWeekendFlightSpecification implements Specification<Flight> {
    
    /**
     * Hour threshold for Friday evening flights (6:00 PM).
     */
    private static final int FRIDAY_EVENING_HOUR = 18;
    
    @Override
    public boolean isSatisfiedBy(Flight flight) {
        if (flight == null) {
            return false;
        }
        
        LocalDateTime departure = flight.getScheduledDeparture();
        if (departure == null) {
            return false;
        }
        
        DayOfWeek dayOfWeek = departure.getDayOfWeek();
        
        // Saturday and Sunday are always weekend flights
        if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
            return true;
        }
        
        // Friday evening flights (after 6 PM) are considered weekend flights
        if (dayOfWeek == DayOfWeek.FRIDAY && departure.getHour() >= FRIDAY_EVENING_HOUR) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Checks if the flight departs on a specific weekend day.
     * 
     * @param flight The flight to check
     * @param dayOfWeek The specific day to check for
     * @return true if the flight departs on the specified weekend day
     */
    public boolean departsOnWeekendDay(Flight flight, DayOfWeek dayOfWeek) {
        if (flight == null || dayOfWeek == null) {
            return false;
        }
        
        if (dayOfWeek != DayOfWeek.FRIDAY && 
            dayOfWeek != DayOfWeek.SATURDAY && 
            dayOfWeek != DayOfWeek.SUNDAY) {
            return false;
        }
        
        LocalDateTime departure = flight.getScheduledDeparture();
        if (departure == null) {
            return false;
        }
        
        DayOfWeek flightDay = departure.getDayOfWeek();
        
        if (dayOfWeek == DayOfWeek.FRIDAY) {
            // For Friday, check if it's evening
            return flightDay == DayOfWeek.FRIDAY && departure.getHour() >= FRIDAY_EVENING_HOUR;
        }
        
        return flightDay == dayOfWeek;
    }
    
    /**
     * Checks if the flight is a Sunday flight (typically highest demand).
     * 
     * @param flight The flight to check
     * @return true if the flight departs on Sunday
     */
    public boolean isSundayFlight(Flight flight) {
        return departsOnWeekendDay(flight, DayOfWeek.SUNDAY);
    }
    
    /**
     * Checks if the flight is a Friday evening flight.
     * 
     * @param flight The flight to check
     * @return true if the flight departs on Friday evening
     */
    public boolean isFridayEveningFlight(Flight flight) {
        return departsOnWeekendDay(flight, DayOfWeek.FRIDAY);
    }
    
    /**
     * Gets the weekend surcharge percentage based on the specific day.
     * This demonstrates how specifications can provide additional business logic.
     * 
     * @param flight The flight to evaluate
     * @return Suggested surcharge percentage (0 if not a weekend flight)
     */
    public double getSuggestedSurchargePercentage(Flight flight) {
        if (!isSatisfiedBy(flight)) {
            return 0.0;
        }
        
        DayOfWeek dayOfWeek = flight.getScheduledDeparture().getDayOfWeek();
        
        return switch (dayOfWeek) {
            case FRIDAY -> 15.0;  // 15% surcharge for Friday evening
            case SATURDAY -> 20.0; // 20% surcharge for Saturday
            case SUNDAY -> 25.0;   // 25% surcharge for Sunday (highest demand)
            default -> 0.0;
        };
    }
}
