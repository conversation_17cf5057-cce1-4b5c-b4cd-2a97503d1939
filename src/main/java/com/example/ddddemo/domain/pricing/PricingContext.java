package com.example.ddddemo.domain.pricing;

import com.example.ddddemo.domain.shared.ValueObject;
import java.util.Objects;

/**
 * PricingContext Value Object containing additional context for price calculations.
 * 
 * This Value Object encapsulates various contextual factors that influence
 * pricing but don't belong to the core Flight or PricingRule aggregates.
 * It demonstrates how Value Objects can be used to pass complex data
 * between domain services while maintaining immutability and validation.
 * 
 * Examples of pricing context:
 * - Customer loyalty status and benefits
 * - Group booking information
 * - Corporate contract details
 * - Promotional codes or campaigns
 * - Booking channel (web, mobile, agent)
 * - Payment method preferences
 */
public class PricingContext implements ValueObject {
    
    /**
     * Whether the customer has a loyalty membership.
     */
    private final boolean hasLoyaltyMembership;
    
    /**
     * Loyalty discount percentage (0-100).
     */
    private final double loyaltyDiscountPercentage;
    
    /**
     * Whether this is a group booking.
     */
    private final boolean isGroupBooking;
    
    /**
     * Group discount percentage (0-100).
     */
    private final double groupDiscountPercentage;
    
    /**
     * Whether the customer has a corporate contract.
     */
    private final boolean hasCorporateContract;
    
    /**
     * Corporate discount percentage (0-100).
     */
    private final double corporateDiscountPercentage;
    
    /**
     * Promotional code applied to the booking.
     */
    private final String promotionalCode;
    
    /**
     * Booking channel used for the reservation.
     */
    private final BookingChannel bookingChannel;
    
    /**
     * Creates a new PricingContext.
     */
    public PricingContext(boolean hasLoyaltyMembership, double loyaltyDiscountPercentage,
                         boolean isGroupBooking, double groupDiscountPercentage,
                         boolean hasCorporateContract, double corporateDiscountPercentage,
                         String promotionalCode, BookingChannel bookingChannel) {
        
        validateDiscountPercentage(loyaltyDiscountPercentage, "Loyalty discount");
        validateDiscountPercentage(groupDiscountPercentage, "Group discount");
        validateDiscountPercentage(corporateDiscountPercentage, "Corporate discount");
        
        this.hasLoyaltyMembership = hasLoyaltyMembership;
        this.loyaltyDiscountPercentage = loyaltyDiscountPercentage;
        this.isGroupBooking = isGroupBooking;
        this.groupDiscountPercentage = groupDiscountPercentage;
        this.hasCorporateContract = hasCorporateContract;
        this.corporateDiscountPercentage = corporateDiscountPercentage;
        this.promotionalCode = promotionalCode;
        this.bookingChannel = Objects.requireNonNull(bookingChannel, "Booking channel cannot be null");
    }
    
    private void validateDiscountPercentage(double percentage, String discountType) {
        if (percentage < 0 || percentage > 100) {
            throw new IllegalArgumentException(
                discountType + " percentage must be between 0 and 100: " + percentage);
        }
    }
    
    // Getters
    public boolean hasLoyaltyMembership() {
        return hasLoyaltyMembership;
    }
    
    public double getLoyaltyDiscountPercentage() {
        return loyaltyDiscountPercentage;
    }
    
    public boolean isGroupBooking() {
        return isGroupBooking;
    }
    
    public double getGroupDiscountPercentage() {
        return groupDiscountPercentage;
    }
    
    public boolean hasCorporateContract() {
        return hasCorporateContract;
    }
    
    public double getCorporateDiscountPercentage() {
        return corporateDiscountPercentage;
    }
    
    public String getPromotionalCode() {
        return promotionalCode;
    }
    
    public BookingChannel getBookingChannel() {
        return bookingChannel;
    }
    
    /**
     * Factory method for standard individual booking context.
     */
    public static PricingContext standardBooking(BookingChannel channel) {
        return new PricingContext(false, 0, false, 0, false, 0, null, channel);
    }
    
    /**
     * Factory method for loyalty member booking context.
     */
    public static PricingContext loyaltyMemberBooking(double discountPercentage, BookingChannel channel) {
        return new PricingContext(true, discountPercentage, false, 0, false, 0, null, channel);
    }
    
    /**
     * Factory method for group booking context.
     */
    public static PricingContext groupBooking(double discountPercentage, BookingChannel channel) {
        return new PricingContext(false, 0, true, discountPercentage, false, 0, null, channel);
    }
    
    /**
     * Factory method for corporate booking context.
     */
    public static PricingContext corporateBooking(double discountPercentage, BookingChannel channel) {
        return new PricingContext(false, 0, false, 0, true, discountPercentage, null, channel);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PricingContext that = (PricingContext) obj;
        return hasLoyaltyMembership == that.hasLoyaltyMembership &&
               Double.compare(that.loyaltyDiscountPercentage, loyaltyDiscountPercentage) == 0 &&
               isGroupBooking == that.isGroupBooking &&
               Double.compare(that.groupDiscountPercentage, groupDiscountPercentage) == 0 &&
               hasCorporateContract == that.hasCorporateContract &&
               Double.compare(that.corporateDiscountPercentage, corporateDiscountPercentage) == 0 &&
               Objects.equals(promotionalCode, that.promotionalCode) &&
               bookingChannel == that.bookingChannel;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(hasLoyaltyMembership, loyaltyDiscountPercentage, isGroupBooking,
                          groupDiscountPercentage, hasCorporateContract, corporateDiscountPercentage,
                          promotionalCode, bookingChannel);
    }
    
    @Override
    public String toString() {
        return String.format("PricingContext{loyalty=%s, group=%s, corporate=%s, channel=%s}",
                           hasLoyaltyMembership, isGroupBooking, hasCorporateContract, bookingChannel);
    }
    
    /**
     * Enumeration for booking channels.
     */
    public enum BookingChannel {
        WEB("Online web booking"),
        MOBILE("Mobile app booking"),
        CALL_CENTER("Call center booking"),
        TRAVEL_AGENT("Travel agent booking"),
        CORPORATE_PORTAL("Corporate booking portal"),
        KIOSK("Airport kiosk booking");
        
        private final String description;
        
        BookingChannel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
