package com.example.ddddemo.domain.pricing;

import com.example.ddddemo.domain.shared.AggregateRoot;
import com.example.ddddemo.domain.shared.Money;
import com.example.ddddemo.domain.flight.Route;
import java.time.LocalDateTime;
import java.util.*;

/**
 * PricingRule Aggregate Root representing pricing rules in our aviation system.
 * 
 * PricingRule is an Aggregate Root because:
 * - It has a clear identity and lifecycle
 * - It controls complex pricing logic and factors
 * - It maintains pricing consistency and business rules
 * - It publishes domain events when rules change
 * - External systems interact with pricing rules for fare calculation
 * 
 * The PricingRule aggregate boundary includes:
 * - PricingRule (Aggregate Root)
 * - PricingFactor (Value Object collection)
 * - Route applicability (Value Object)
 * - Validity period (Value Objects)
 * 
 * Key business invariants maintained:
 * - Rules must have valid effective periods
 * - Pricing factors must be consistent and non-conflicting
 * - Rules must apply to valid routes
 * - Priority ordering must be maintained for rule application
 * 
 * This demonstrates advanced DDD concepts:
 * - Complex aggregate with multiple value objects
 * - Business rule validation and invariants
 * - Domain events for pricing changes
 * - Specification pattern integration
 */
public class PricingRule extends AggregateRoot<UUID> {
    
    /**
     * Unique identifier for this pricing rule.
     */
    private final UUID ruleId;
    
    /**
     * Human-readable name for this pricing rule.
     */
    private String ruleName;
    
    /**
     * Detailed description of what this rule does.
     */
    private String description;
    
    /**
     * Routes this rule applies to (null means applies to all routes).
     */
    private Set<Route> applicableRoutes;
    
    /**
     * Pricing factors that make up this rule.
     */
    private List<PricingFactor> pricingFactors;
    
    /**
     * When this rule becomes effective.
     */
    private LocalDateTime effectiveFrom;
    
    /**
     * When this rule expires (null means no expiration).
     */
    private LocalDateTime effectiveTo;
    
    /**
     * Priority for rule application (higher numbers = higher priority).
     */
    private int priority;
    
    /**
     * Whether this rule is currently active.
     */
    private boolean active;
    
    /**
     * Rule type for categorization.
     */
    private PricingRuleType ruleType;
    
    /**
     * Creates a new PricingRule.
     * 
     * @param ruleName Name of the rule
     * @param description Description of the rule
     * @param ruleType Type of pricing rule
     * @param effectiveFrom When the rule becomes effective
     * @param priority Priority for rule application
     */
    public PricingRule(String ruleName, String description, PricingRuleType ruleType,
                      LocalDateTime effectiveFrom, int priority) {
        super();
        
        this.ruleId = UUID.randomUUID();
        setRuleName(ruleName);
        setDescription(description);
        this.ruleType = Objects.requireNonNull(ruleType, "Rule type cannot be null");
        setEffectivePeriod(effectiveFrom, null);
        setPriority(priority);
        
        this.applicableRoutes = new HashSet<>();
        this.pricingFactors = new ArrayList<>();
        this.active = true;
        
        // Publish domain event
        addDomainEvent(new PricingRuleCreatedEvent(ruleId, ruleName, ruleType, effectiveFrom));
    }
    
    private void setRuleName(String ruleName) {
        if (ruleName == null || ruleName.trim().isEmpty()) {
            throw new IllegalArgumentException("Rule name cannot be null or empty");
        }
        this.ruleName = ruleName.trim();
    }
    
    private void setDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            throw new IllegalArgumentException("Description cannot be null or empty");
        }
        this.description = description.trim();
    }
    
    private void setPriority(int priority) {
        if (priority < 0) {
            throw new IllegalArgumentException("Priority cannot be negative");
        }
        this.priority = priority;
    }
    
    // Getters
    public UUID getRuleId() {
        return ruleId;
    }
    
    public String getRuleName() {
        return ruleName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public Set<Route> getApplicableRoutes() {
        return Collections.unmodifiableSet(applicableRoutes);
    }
    
    public List<PricingFactor> getPricingFactors() {
        return Collections.unmodifiableList(pricingFactors);
    }
    
    public LocalDateTime getEffectiveFrom() {
        return effectiveFrom;
    }
    
    public LocalDateTime getEffectiveTo() {
        return effectiveTo;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public PricingRuleType getRuleType() {
        return ruleType;
    }
    
    /**
     * Sets the effective period for this pricing rule.
     * 
     * @param effectiveFrom When the rule becomes effective
     * @param effectiveTo When the rule expires (null for no expiration)
     */
    public void setEffectivePeriod(LocalDateTime effectiveFrom, LocalDateTime effectiveTo) {
        Objects.requireNonNull(effectiveFrom, "Effective from date cannot be null");
        
        if (effectiveTo != null && !effectiveTo.isAfter(effectiveFrom)) {
            throw new IllegalArgumentException("Effective to date must be after effective from date");
        }
        
        this.effectiveFrom = effectiveFrom;
        this.effectiveTo = effectiveTo;
        
        addDomainEvent(new PricingRuleUpdatedEvent(ruleId, "Effective period updated"));
    }
    
    /**
     * Adds a route that this pricing rule applies to.
     * If no routes are specified, the rule applies to all routes.
     * 
     * @param route The route to add
     */
    public void addApplicableRoute(Route route) {
        Objects.requireNonNull(route, "Route cannot be null");
        
        if (applicableRoutes.add(route)) {
            addDomainEvent(new PricingRuleUpdatedEvent(ruleId, "Route added: " + route));
        }
    }
    
    /**
     * Removes a route from the applicable routes.
     * 
     * @param route The route to remove
     */
    public void removeApplicableRoute(Route route) {
        if (applicableRoutes.remove(route)) {
            addDomainEvent(new PricingRuleUpdatedEvent(ruleId, "Route removed: " + route));
        }
    }
    
    /**
     * Adds a pricing factor to this rule.
     * 
     * @param factor The pricing factor to add
     */
    public void addPricingFactor(PricingFactor factor) {
        Objects.requireNonNull(factor, "Pricing factor cannot be null");
        
        // Business rule: Don't allow duplicate factor types
        boolean hasConflict = pricingFactors.stream()
            .anyMatch(existing -> existing.getType() == factor.getType());
        
        if (hasConflict) {
            throw new IllegalArgumentException(
                "Pricing rule already contains a factor of type: " + factor.getType());
        }
        
        pricingFactors.add(factor);
        addDomainEvent(new PricingRuleUpdatedEvent(ruleId, "Pricing factor added: " + factor.getType()));
    }
    
    /**
     * Removes a pricing factor by type.
     * 
     * @param factorType The type of factor to remove
     */
    public void removePricingFactor(PricingFactor.PricingFactorType factorType) {
        boolean removed = pricingFactors.removeIf(factor -> factor.getType() == factorType);
        
        if (removed) {
            addDomainEvent(new PricingRuleUpdatedEvent(ruleId, "Pricing factor removed: " + factorType));
        }
    }
    
    /**
     * Checks if this rule applies to the given route.
     * 
     * @param route The route to check
     * @return true if the rule applies to the route
     */
    public boolean appliesTo(Route route) {
        // If no specific routes are defined, rule applies to all routes
        if (applicableRoutes.isEmpty()) {
            return true;
        }
        
        return applicableRoutes.contains(route);
    }
    
    /**
     * Checks if this rule is currently effective.
     * 
     * @return true if the rule is active and within its effective period
     */
    public boolean isCurrentlyEffective() {
        if (!active) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        if (now.isBefore(effectiveFrom)) {
            return false;
        }
        
        if (effectiveTo != null && now.isAfter(effectiveTo)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Applies this pricing rule to a base price.
     * This is the core business logic of the pricing system.
     * 
     * @param basePrice The base price to apply the rule to
     * @return The adjusted price after applying all pricing factors
     */
    public Money applyRule(Money basePrice) {
        Objects.requireNonNull(basePrice, "Base price cannot be null");
        
        if (!isCurrentlyEffective()) {
            return basePrice; // Rule not effective, return original price
        }
        
        Money adjustedPrice = basePrice;
        
        // Apply each pricing factor in sequence
        for (PricingFactor factor : pricingFactors) {
            adjustedPrice = adjustedPrice.multiply(factor.getAdjustmentValue());
        }
        
        return adjustedPrice;
    }
    
    /**
     * Activates this pricing rule.
     */
    public void activate() {
        if (!active) {
            this.active = true;
            addDomainEvent(new PricingRuleActivatedEvent(ruleId, ruleName));
        }
    }
    
    /**
     * Deactivates this pricing rule.
     */
    public void deactivate() {
        if (active) {
            this.active = false;
            addDomainEvent(new PricingRuleDeactivatedEvent(ruleId, ruleName));
        }
    }
    
    /**
     * Updates the rule priority.
     * 
     * @param newPriority The new priority value
     */
    public void updatePriority(int newPriority) {
        if (newPriority != this.priority) {
            int oldPriority = this.priority;
            setPriority(newPriority);
            addDomainEvent(new PricingRuleUpdatedEvent(ruleId, 
                String.format("Priority changed from %d to %d", oldPriority, newPriority)));
        }
    }
    
    @Override
    protected void validateInvariants() {
        // Validate effective period
        if (effectiveTo != null && !effectiveTo.isAfter(effectiveFrom)) {
            throw new IllegalStateException("Effective to date must be after effective from date");
        }
        
        // Validate that we have at least one pricing factor if rule is active
        if (active && pricingFactors.isEmpty()) {
            throw new IllegalStateException("Active pricing rule must have at least one pricing factor");
        }
        
        // Validate no duplicate factor types
        Set<PricingFactor.PricingFactorType> factorTypes = new HashSet<>();
        for (PricingFactor factor : pricingFactors) {
            if (!factorTypes.add(factor.getType())) {
                throw new IllegalStateException("Duplicate pricing factor type: " + factor.getType());
            }
        }
    }
    
    @Override
    public String toString() {
        return String.format("PricingRule{id=%s, name='%s', type=%s, active=%s, priority=%d}", 
                           ruleId, ruleName, ruleType, active, priority);
    }
    
    /**
     * Enumeration for pricing rule types.
     */
    public enum PricingRuleType {
        BASE_PRICING("Base pricing rules for standard fares"),
        PROMOTIONAL("Promotional pricing for marketing campaigns"),
        SEASONAL("Seasonal pricing adjustments"),
        DYNAMIC("Dynamic pricing based on demand and availability"),
        LOYALTY("Loyalty program pricing benefits"),
        CORPORATE("Corporate contract pricing"),
        GROUP("Group booking pricing rules");
        
        private final String description;
        
        PricingRuleType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
