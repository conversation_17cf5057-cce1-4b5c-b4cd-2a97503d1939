package com.example.ddddemo.domain.pricing;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.util.Objects;
import java.util.UUID;

/**
 * Domain Event published when a pricing rule is updated.
 * 
 * This event represents changes to existing pricing rules.
 * It's important for maintaining audit trails and notifying
 * systems that depend on pricing information.
 */
public class PricingRuleUpdatedEvent extends DomainEvent {
    
    /**
     * The unique identifier of the updated pricing rule.
     */
    private final UUID ruleId;
    
    /**
     * Description of what was updated.
     */
    private final String updateDescription;
    
    /**
     * Creates a new PricingRuleUpdatedEvent.
     * 
     * @param ruleId The pricing rule identifier
     * @param updateDescription Description of the update
     */
    public PricingRuleUpdatedEvent(UUID ruleId, String updateDescription) {
        super();
        this.ruleId = Objects.requireNonNull(ruleId, "Rule ID cannot be null");
        this.updateDescription = Objects.requireNonNull(updateDescription, "Update description cannot be null");
    }
    
    public UUID getRuleId() {
        return ruleId;
    }
    
    public String getUpdateDescription() {
        return updateDescription;
    }
    
    @Override
    public String toString() {
        return String.format("PricingRuleUpdatedEvent{ruleId=%s, updateDescription='%s'}", 
                           ruleId, updateDescription);
    }
}
