package com.example.ddddemo.domain.pricing.specifications;

import com.example.ddddemo.domain.customer.Booking;
import com.example.ddddemo.domain.flight.Flight;
import com.example.ddddemo.domain.shared.ValueObject;
import java.util.Objects;

/**
 * BookingContext Value Object that provides context for booking-related specifications.
 * 
 * This Value Object encapsulates the data needed by specifications that evaluate
 * business rules involving both bookings and flights. It demonstrates how Value Objects
 * can be used to pass complex context between domain services and specifications
 * while maintaining immutability and encapsulation.
 * 
 * The context pattern is useful when specifications need access to multiple
 * aggregates or when the evaluation criteria involve relationships between
 * different domain objects.
 * 
 * Examples of specifications that might use this context:
 * - IsEligibleForAdvanceBookingDiscountSpecification
 * - IsEligibleForLoyaltyDiscountSpecification
 * - IsValidForSeatUpgradeSpecification
 * - CanBeCancelledWithoutPenaltySpecification
 */
public class BookingContext implements ValueObject {
    
    /**
     * The booking being evaluated.
     */
    private final Booking booking;
    
    /**
     * The flight associated with the booking.
     */
    private final Flight flight;
    
    /**
     * Creates a new BookingContext.
     * 
     * @param booking The booking to include in the context
     * @param flight The flight associated with the booking
     */
    public BookingContext(Booking booking, Flight flight) {
        this.booking = Objects.requireNonNull(booking, "Booking cannot be null");
        this.flight = Objects.requireNonNull(flight, "Flight cannot be null");
        
        // Validate that the booking is for the provided flight
        if (!booking.getFlightId().equals(flight.getFlightId())) {
            throw new IllegalArgumentException(
                String.format("Booking flight ID %s does not match provided flight ID %s",
                            booking.getFlightId(), flight.getFlightId()));
        }
    }
    
    /**
     * Gets the booking from this context.
     * 
     * @return The booking
     */
    public Booking getBooking() {
        return booking;
    }
    
    /**
     * Gets the flight from this context.
     * 
     * @return The flight
     */
    public Flight getFlight() {
        return flight;
    }
    
    /**
     * Checks if the booking is for a domestic flight.
     * This is a convenience method that delegates to the flight.
     * 
     * @return true if the flight is domestic
     */
    public boolean isDomesticFlight() {
        return flight.isDomestic();
    }
    
    /**
     * Checks if the booking is for an international flight.
     * 
     * @return true if the flight is international
     */
    public boolean isInternationalFlight() {
        return !isDomesticFlight();
    }
    
    /**
     * Checks if the booking contains any passengers with frequent flyer numbers.
     * This is useful for loyalty-related specifications.
     * 
     * @return true if any passenger has a frequent flyer number
     */
    public boolean hasFrequentFlyerPassengers() {
        return booking.getPassengers().stream()
            .anyMatch(passenger -> passenger.hasFrequentFlyerNumber());
    }
    
    /**
     * Checks if the booking contains any child or infant passengers.
     * This is useful for family-related pricing specifications.
     * 
     * @return true if the booking contains children or infants
     */
    public boolean hasChildrenOrInfants() {
        return booking.getChildCount() > 0 || booking.getInfantCount() > 0;
    }
    
    /**
     * Checks if this is a group booking (more than 4 passengers).
     * This is useful for group discount specifications.
     * 
     * @return true if the booking has more than 4 passengers
     */
    public boolean isGroupBooking() {
        return booking.getPassengerCount() > 4;
    }
    
    /**
     * Checks if the flight involves a major hub airport.
     * This is useful for route-based pricing specifications.
     * 
     * @return true if either origin or destination is a major hub
     */
    public boolean involvesHubAirport() {
        return flight.getRoute().involvesHub();
    }
    
    /**
     * Gets the flight load factor (percentage of seats occupied).
     * This is useful for demand-based pricing specifications.
     * 
     * @return The flight load factor as a percentage
     */
    public double getFlightLoadFactor() {
        return flight.getLoadFactor();
    }
    
    /**
     * Checks if the flight has high demand (load factor > 80%).
     * This is useful for surge pricing specifications.
     * 
     * @return true if the flight has high demand
     */
    public boolean isHighDemandFlight() {
        return getFlightLoadFactor() > 80.0;
    }
    
    /**
     * Checks if the flight has low demand (load factor < 50%).
     * This is useful for promotional pricing specifications.
     * 
     * @return true if the flight has low demand
     */
    public boolean isLowDemandFlight() {
        return getFlightLoadFactor() < 50.0;
    }
    
    /**
     * Factory method for creating a BookingContext.
     * This provides a more fluent API for context creation.
     * 
     * @param booking The booking
     * @param flight The flight
     * @return A new BookingContext instance
     */
    public static BookingContext of(Booking booking, Flight flight) {
        return new BookingContext(booking, flight);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        BookingContext that = (BookingContext) obj;
        return Objects.equals(booking, that.booking) &&
               Objects.equals(flight, that.flight);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(booking, flight);
    }
    
    @Override
    public String toString() {
        return String.format("BookingContext{booking=%s, flight=%s}", 
                           booking.getBookingReference(), flight.getFlightNumber());
    }
}
