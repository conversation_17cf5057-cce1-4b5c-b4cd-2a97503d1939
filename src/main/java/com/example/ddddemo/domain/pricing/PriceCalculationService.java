package com.example.ddddemo.domain.pricing;

import com.example.ddddemo.domain.shared.DomainService;
import com.example.ddddemo.domain.shared.Money;
import com.example.ddddemo.domain.flight.Flight;
import com.example.ddddemo.domain.flight.Route;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Domain Service for calculating ticket prices based on pricing rules.
 * 
 * This is a classic example of a Domain Service in DDD because:
 * 1. The price calculation logic doesn't naturally belong to any single entity
 * 2. It involves multiple aggregates (Flight, PricingRule, potentially Customer)
 * 3. It represents a significant business process
 * 4. It contains complex domain logic that needs to be reusable
 * 
 * The service orchestrates the application of multiple pricing rules
 * to calculate the final ticket price. It demonstrates:
 * - Coordination between multiple domain objects
 * - Complex business logic encapsulation
 * - Stateless service design
 * - Domain-focused behavior
 * 
 * Key responsibilities:
 * - Apply pricing rules in the correct order (by priority)
 * - Handle rule conflicts and overlaps
 * - Ensure business rules are followed
 * - Provide transparent price calculation logic
 */
public class PriceCalculationService implements DomainService {
    
    /**
     * Calculates the ticket price for a flight using applicable pricing rules.
     * 
     * This is the main business operation that demonstrates how Domain Services
     * coordinate between multiple aggregates to perform complex business logic.
     * 
     * @param flight The flight to calculate price for
     * @param seatClass The seat class being booked
     * @param bookingDate When the booking is being made
     * @param applicableRules List of pricing rules that could apply
     * @return The calculated ticket price
     */
    public Money calculatePrice(Flight flight, Flight.SeatClass seatClass, 
                               LocalDateTime bookingDate, List<PricingRule> applicableRules) {
        
        Objects.requireNonNull(flight, "Flight cannot be null");
        Objects.requireNonNull(seatClass, "Seat class cannot be null");
        Objects.requireNonNull(bookingDate, "Booking date cannot be null");
        Objects.requireNonNull(applicableRules, "Applicable rules cannot be null");
        
        // Start with the flight's base price
        Money currentPrice = flight.getBasePrice();
        
        // Apply seat class multiplier
        currentPrice = applySeatClassPricing(currentPrice, seatClass);
        
        // Filter and sort applicable rules
        List<PricingRule> effectiveRules = getEffectiveRules(flight.getRoute(), applicableRules);
        
        // Apply each pricing rule in priority order
        for (PricingRule rule : effectiveRules) {
            currentPrice = rule.applyRule(currentPrice);
        }
        
        return currentPrice;
    }
    
    /**
     * Calculates price with additional context for more sophisticated pricing.
     * This overload demonstrates how Domain Services can provide multiple
     * interfaces for different use cases.
     * 
     * @param flight The flight to calculate price for
     * @param seatClass The seat class being booked
     * @param bookingDate When the booking is being made
     * @param applicableRules List of pricing rules that could apply
     * @param context Additional pricing context
     * @return The calculated ticket price with detailed breakdown
     */
    public PriceCalculationResult calculatePriceWithBreakdown(Flight flight, Flight.SeatClass seatClass,
                                                             LocalDateTime bookingDate, 
                                                             List<PricingRule> applicableRules,
                                                             PricingContext context) {
        
        Objects.requireNonNull(context, "Pricing context cannot be null");
        
        PriceCalculationResult.Builder resultBuilder = new PriceCalculationResult.Builder();
        
        // Start with base price
        Money currentPrice = flight.getBasePrice();
        resultBuilder.setBasePrice(currentPrice);
        
        // Apply seat class pricing
        Money seatClassPrice = applySeatClassPricing(currentPrice, seatClass);
        if (!seatClassPrice.equals(currentPrice)) {
            resultBuilder.addAdjustment("Seat Class (" + seatClass + ")", 
                                      seatClassPrice.subtract(currentPrice));
        }
        currentPrice = seatClassPrice;
        
        // Apply pricing rules
        List<PricingRule> effectiveRules = getEffectiveRules(flight.getRoute(), applicableRules);
        
        for (PricingRule rule : effectiveRules) {
            Money beforeRule = currentPrice;
            currentPrice = rule.applyRule(currentPrice);
            
            if (!currentPrice.equals(beforeRule)) {
                Money adjustment = currentPrice.subtract(beforeRule);
                resultBuilder.addAdjustment(rule.getRuleName(), adjustment);
            }
        }
        
        // Apply context-specific adjustments
        currentPrice = applyContextualAdjustments(currentPrice, context, resultBuilder);
        
        return resultBuilder.setFinalPrice(currentPrice).build();
    }
    
    /**
     * Applies seat class pricing multipliers.
     * This demonstrates domain logic that could be extracted to a separate
     * value object or specification if it becomes more complex.
     */
    private Money applySeatClassPricing(Money basePrice, Flight.SeatClass seatClass) {
        return switch (seatClass) {
            case ECONOMY -> basePrice; // No multiplier for economy
            case BUSINESS -> basePrice.multiply(java.math.BigDecimal.valueOf(2.5)); // 2.5x for business
            case FIRST -> basePrice.multiply(java.math.BigDecimal.valueOf(4.0)); // 4x for first class
        };
    }
    
    /**
     * Filters pricing rules to only those that are effective and applicable.
     * Returns rules sorted by priority (highest first).
     */
    private List<PricingRule> getEffectiveRules(Route route, List<PricingRule> allRules) {
        return allRules.stream()
            .filter(PricingRule::isCurrentlyEffective)
            .filter(rule -> rule.appliesTo(route))
            .sorted((r1, r2) -> Integer.compare(r2.getPriority(), r1.getPriority())) // Descending priority
            .toList();
    }
    
    /**
     * Applies contextual adjustments based on additional pricing context.
     * This demonstrates how Domain Services can handle complex business logic
     * that involves multiple factors.
     */
    private Money applyContextualAdjustments(Money currentPrice, PricingContext context, 
                                           PriceCalculationResult.Builder resultBuilder) {
        
        Money adjustedPrice = currentPrice;
        
        // Apply loyalty discount if applicable
        if (context.hasLoyaltyMembership()) {
            Money loyaltyDiscount = currentPrice.multiplyByPercentage(-context.getLoyaltyDiscountPercentage());
            adjustedPrice = adjustedPrice.add(loyaltyDiscount);
            resultBuilder.addAdjustment("Loyalty Discount", loyaltyDiscount);
        }
        
        // Apply group booking discount if applicable
        if (context.isGroupBooking()) {
            Money groupDiscount = currentPrice.multiplyByPercentage(-context.getGroupDiscountPercentage());
            adjustedPrice = adjustedPrice.add(groupDiscount);
            resultBuilder.addAdjustment("Group Booking Discount", groupDiscount);
        }
        
        // Apply corporate contract pricing if applicable
        if (context.hasCorporateContract()) {
            Money corporateDiscount = currentPrice.multiplyByPercentage(-context.getCorporateDiscountPercentage());
            adjustedPrice = adjustedPrice.add(corporateDiscount);
            resultBuilder.addAdjustment("Corporate Contract", corporateDiscount);
        }
        
        return adjustedPrice;
    }
    
    /**
     * Validates that a calculated price meets business rules.
     * This demonstrates how Domain Services can enforce business invariants.
     * 
     * @param calculatedPrice The calculated price to validate
     * @param basePrice The original base price
     * @throws IllegalStateException if the price violates business rules
     */
    public void validateCalculatedPrice(Money calculatedPrice, Money basePrice) {
        Objects.requireNonNull(calculatedPrice, "Calculated price cannot be null");
        Objects.requireNonNull(basePrice, "Base price cannot be null");
        
        // Business rule: Final price cannot be negative
        if (!calculatedPrice.isPositive() && !calculatedPrice.isZero()) {
            throw new IllegalStateException("Calculated price cannot be negative: " + calculatedPrice);
        }
        
        // Business rule: Final price cannot be more than 10x the base price
        Money maxAllowedPrice = basePrice.multiply(java.math.BigDecimal.valueOf(10));
        if (calculatedPrice.compareTo(maxAllowedPrice) > 0) {
            throw new IllegalStateException(
                String.format("Calculated price %s exceeds maximum allowed price %s", 
                            calculatedPrice, maxAllowedPrice));
        }
        
        // Business rule: Final price cannot be less than 10% of base price
        Money minAllowedPrice = basePrice.multiply(java.math.BigDecimal.valueOf(0.1));
        if (calculatedPrice.compareTo(minAllowedPrice) < 0) {
            throw new IllegalStateException(
                String.format("Calculated price %s is below minimum allowed price %s", 
                            calculatedPrice, minAllowedPrice));
        }
    }
    
    /**
     * Estimates the price impact of adding a new pricing rule.
     * This demonstrates how Domain Services can provide analytical capabilities.
     * 
     * @param currentRules Existing pricing rules
     * @param newRule The new rule to evaluate
     * @param sampleFlights Sample flights to test against
     * @return Estimated price impact analysis
     */
    public PriceImpactAnalysis estimatePriceImpact(List<PricingRule> currentRules, 
                                                  PricingRule newRule, 
                                                  List<Flight> sampleFlights) {
        
        // This would contain complex analysis logic
        // For brevity, returning a placeholder
        return new PriceImpactAnalysis(newRule.getRuleId(), "Analysis not implemented in demo");
    }
}
