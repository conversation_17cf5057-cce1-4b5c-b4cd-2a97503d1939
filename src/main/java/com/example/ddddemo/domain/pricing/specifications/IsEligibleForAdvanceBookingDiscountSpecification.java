package com.example.ddddemo.domain.pricing.specifications;

import com.example.ddddemo.domain.shared.Specification;
import com.example.ddddemo.domain.customer.Booking;
import com.example.ddddemo.domain.flight.Flight;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * Specification that determines if a booking is eligible for advance booking discount.
 * 
 * This is a concrete implementation of the Specification pattern that encapsulates
 * the business rule for advance booking discounts. The rule states that bookings
 * made more than 30 days before the flight departure are eligible for a discount.
 * 
 * This demonstrates several DDD concepts:
 * - Encapsulation of business rules in dedicated classes
 * - Reusable and testable business logic
 * - Clear expression of domain concepts
 * - Composable specifications using logical operators
 * 
 * Business Rule: A booking is eligible for advance booking discount if:
 * 1. The booking is made at least 30 days before flight departure
 * 2. The booking is in PENDING or CONFIRMED status
 * 3. The flight is not cancelled
 * 
 * Usage Example:
 * ```java
 * Specification<BookingContext> advanceBookingSpec = new IsEligibleForAdvanceBookingDiscountSpecification();
 * if (advanceBookingSpec.isSatisfiedBy(bookingContext)) {
 *     // Apply advance booking discount
 * }
 * ```
 */
public class IsEligibleForAdvanceBookingDiscountSpecification implements Specification<BookingContext> {
    
    /**
     * Minimum number of days before departure to qualify for advance booking discount.
     */
    private static final int MINIMUM_ADVANCE_DAYS = 30;
    
    @Override
    public boolean isSatisfiedBy(BookingContext context) {
        if (context == null) {
            return false;
        }
        
        Booking booking = context.getBooking();
        Flight flight = context.getFlight();
        
        if (booking == null || flight == null) {
            return false;
        }
        
        // Check if booking is in valid status
        if (!isBookingInValidStatus(booking)) {
            return false;
        }
        
        // Check if flight is not cancelled
        if (flight.getStatus() == Flight.FlightStatus.CANCELLED) {
            return false;
        }
        
        // Check if booking was made sufficiently in advance
        return isBookingMadeInAdvance(booking.getBookingDate(), flight.getScheduledDeparture());
    }
    
    /**
     * Checks if the booking is in a valid status for discount eligibility.
     */
    private boolean isBookingInValidStatus(Booking booking) {
        return booking.getStatus() == Booking.BookingStatus.PENDING ||
               booking.getStatus() == Booking.BookingStatus.CONFIRMED;
    }
    
    /**
     * Checks if the booking was made sufficiently in advance of the flight departure.
     */
    private boolean isBookingMadeInAdvance(LocalDateTime bookingDate, LocalDateTime departureDate) {
        long daysBetween = ChronoUnit.DAYS.between(bookingDate, departureDate);
        return daysBetween >= MINIMUM_ADVANCE_DAYS;
    }
    
    /**
     * Gets the minimum advance days required for the discount.
     * This method allows for easy testing and configuration.
     * 
     * @return The minimum number of advance days
     */
    public int getMinimumAdvanceDays() {
        return MINIMUM_ADVANCE_DAYS;
    }
    
    /**
     * Calculates how many days in advance the booking was made.
     * This can be useful for graduated discount calculations.
     * 
     * @param context The booking context
     * @return Number of days in advance, or -1 if context is invalid
     */
    public long calculateAdvanceDays(BookingContext context) {
        if (context == null || context.getBooking() == null || context.getFlight() == null) {
            return -1;
        }
        
        return ChronoUnit.DAYS.between(
            context.getBooking().getBookingDate(),
            context.getFlight().getScheduledDeparture()
        );
    }
    
    /**
     * Checks if the booking qualifies for a higher tier advance booking discount.
     * This demonstrates how specifications can be extended for more complex rules.
     * 
     * @param context The booking context
     * @return true if eligible for premium advance booking discount (60+ days)
     */
    public boolean isEligibleForPremiumAdvanceDiscount(BookingContext context) {
        if (!isSatisfiedBy(context)) {
            return false;
        }
        
        return calculateAdvanceDays(context) >= 60;
    }
}
