package com.example.ddddemo.domain.pricing;

import com.example.ddddemo.domain.shared.Money;
import com.example.ddddemo.domain.shared.ValueObject;
import java.util.*;

/**
 * PriceCalculationResult Value Object containing detailed price calculation breakdown.
 * 
 * This Value Object provides transparency into how a ticket price was calculated
 * by showing the base price and all adjustments applied. This is important for:
 * 
 * - Customer transparency: Showing how the final price was determined
 * - Debugging: Understanding why a price calculation produced a specific result
 * - Auditing: Maintaining records of pricing decisions
 * - Analytics: Analyzing the impact of different pricing factors
 * 
 * This demonstrates the Builder pattern within a Value Object, which is useful
 * when the object has many optional components that need to be assembled.
 */
public class PriceCalculationResult implements ValueObject {
    
    /**
     * The base price before any adjustments.
     */
    private final Money basePrice;
    
    /**
     * The final calculated price after all adjustments.
     */
    private final Money finalPrice;
    
    /**
     * List of all price adjustments applied.
     */
    private final List<PriceAdjustment> adjustments;
    
    /**
     * Total amount of all adjustments (positive for surcharges, negative for discounts).
     */
    private final Money totalAdjustments;
    
    /**
     * Private constructor - use Builder to create instances.
     */
    private PriceCalculationResult(Money basePrice, Money finalPrice, List<PriceAdjustment> adjustments) {
        this.basePrice = Objects.requireNonNull(basePrice, "Base price cannot be null");
        this.finalPrice = Objects.requireNonNull(finalPrice, "Final price cannot be null");
        this.adjustments = Collections.unmodifiableList(new ArrayList<>(adjustments));
        this.totalAdjustments = calculateTotalAdjustments();
    }
    
    private Money calculateTotalAdjustments() {
        return adjustments.stream()
            .map(PriceAdjustment::getAmount)
            .reduce(Money.zero(basePrice.getCurrency()), Money::add);
    }
    
    public Money getBasePrice() {
        return basePrice;
    }
    
    public Money getFinalPrice() {
        return finalPrice;
    }
    
    public List<PriceAdjustment> getAdjustments() {
        return adjustments;
    }
    
    public Money getTotalAdjustments() {
        return totalAdjustments;
    }
    
    /**
     * Gets only the surcharges (positive adjustments).
     */
    public List<PriceAdjustment> getSurcharges() {
        return adjustments.stream()
            .filter(adj -> adj.getAmount().isPositive())
            .toList();
    }
    
    /**
     * Gets only the discounts (negative adjustments).
     */
    public List<PriceAdjustment> getDiscounts() {
        return adjustments.stream()
            .filter(adj -> !adj.getAmount().isPositive())
            .toList();
    }
    
    /**
     * Gets the total amount of surcharges.
     */
    public Money getTotalSurcharges() {
        return getSurcharges().stream()
            .map(PriceAdjustment::getAmount)
            .reduce(Money.zero(basePrice.getCurrency()), Money::add);
    }
    
    /**
     * Gets the total amount of discounts (as a positive number).
     */
    public Money getTotalDiscounts() {
        Money totalDiscounts = getDiscounts().stream()
            .map(PriceAdjustment::getAmount)
            .reduce(Money.zero(basePrice.getCurrency()), Money::add);
        
        // Return as positive amount for easier display
        return Money.zero(basePrice.getCurrency()).subtract(totalDiscounts);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PriceCalculationResult that = (PriceCalculationResult) obj;
        return Objects.equals(basePrice, that.basePrice) &&
               Objects.equals(finalPrice, that.finalPrice) &&
               Objects.equals(adjustments, that.adjustments);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(basePrice, finalPrice, adjustments);
    }
    
    @Override
    public String toString() {
        return String.format("PriceCalculationResult{basePrice=%s, finalPrice=%s, adjustments=%d}",
                           basePrice, finalPrice, adjustments.size());
    }
    
    /**
     * Builder for creating PriceCalculationResult instances.
     * This demonstrates the Builder pattern within DDD Value Objects.
     */
    public static class Builder {
        private Money basePrice;
        private Money finalPrice;
        private final List<PriceAdjustment> adjustments = new ArrayList<>();
        
        public Builder setBasePrice(Money basePrice) {
            this.basePrice = basePrice;
            return this;
        }
        
        public Builder setFinalPrice(Money finalPrice) {
            this.finalPrice = finalPrice;
            return this;
        }
        
        public Builder addAdjustment(String description, Money amount) {
            adjustments.add(new PriceAdjustment(description, amount));
            return this;
        }
        
        public Builder addAdjustment(PriceAdjustment adjustment) {
            adjustments.add(adjustment);
            return this;
        }
        
        public PriceCalculationResult build() {
            if (basePrice == null) {
                throw new IllegalStateException("Base price must be set");
            }
            if (finalPrice == null) {
                throw new IllegalStateException("Final price must be set");
            }
            
            return new PriceCalculationResult(basePrice, finalPrice, adjustments);
        }
    }
    
    /**
     * Value Object representing a single price adjustment.
     */
    public static class PriceAdjustment implements ValueObject {
        private final String description;
        private final Money amount;
        
        public PriceAdjustment(String description, Money amount) {
            this.description = Objects.requireNonNull(description, "Description cannot be null");
            this.amount = Objects.requireNonNull(amount, "Amount cannot be null");
        }
        
        public String getDescription() {
            return description;
        }
        
        public Money getAmount() {
            return amount;
        }
        
        public boolean isSurcharge() {
            return amount.isPositive();
        }
        
        public boolean isDiscount() {
            return !amount.isPositive() && !amount.isZero();
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            
            PriceAdjustment that = (PriceAdjustment) obj;
            return Objects.equals(description, that.description) &&
                   Objects.equals(amount, that.amount);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(description, amount);
        }
        
        @Override
        public String toString() {
            String type = isSurcharge() ? "surcharge" : (isDiscount() ? "discount" : "neutral");
            return String.format("PriceAdjustment{description='%s', amount=%s, type=%s}",
                               description, amount, type);
        }
    }
}
