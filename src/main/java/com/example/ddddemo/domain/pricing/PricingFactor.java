package com.example.ddddemo.domain.pricing;

import com.example.ddddemo.domain.shared.ValueObject;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * PricingFactor Value Object representing factors that influence ticket pricing.
 * 
 * Pricing factors are the building blocks of dynamic pricing in the aviation industry.
 * Each factor represents a specific condition or attribute that affects the final
 * ticket price. They can be combined to create complex pricing rules.
 * 
 * Examples of pricing factors:
 * - Seasonal demand (summer vacation, holidays)
 * - Advance booking period (early bird discounts)
 * - Route popularity (high-demand routes cost more)
 * - Day of week (weekend surcharges)
 * - Time of day (red-eye flight discounts)
 * - Seat availability (dynamic pricing based on load factor)
 * - Customer loyalty status (frequent flyer discounts)
 * 
 * This Value Object demonstrates:
 * - Encapsulation of pricing logic
 * - Immutability for thread safety
 * - Self-validation of business rules
 * - Rich domain behavior
 */
public class PricingFactor implements ValueObject {
    
    /**
     * The type of pricing factor (e.g., "SEASONAL", "ADVANCE_BOOKING").
     */
    private final PricingFactorType type;
    
    /**
     * The adjustment value (multiplier or percentage).
     * - Values > 1.0 increase the price (surcharge)
     * - Values < 1.0 decrease the price (discount)
     * - Value = 1.0 has no effect on price
     */
    private final BigDecimal adjustmentValue;
    
    /**
     * Human-readable description of this pricing factor.
     */
    private final String description;
    
    /**
     * Creates a new PricingFactor.
     * 
     * @param type The type of pricing factor
     * @param adjustmentValue The adjustment value (must be positive)
     * @param description Human-readable description
     */
    public PricingFactor(PricingFactorType type, BigDecimal adjustmentValue, String description) {
        validateType(type);
        validateAdjustmentValue(adjustmentValue);
        validateDescription(description);
        
        this.type = type;
        this.adjustmentValue = adjustmentValue;
        this.description = description.trim();
    }
    
    /**
     * Convenience constructor with double adjustment value.
     */
    public PricingFactor(PricingFactorType type, double adjustmentValue, String description) {
        this(type, BigDecimal.valueOf(adjustmentValue), description);
    }
    
    private void validateType(PricingFactorType type) {
        if (type == null) {
            throw new IllegalArgumentException("Pricing factor type cannot be null");
        }
    }
    
    private void validateAdjustmentValue(BigDecimal adjustmentValue) {
        if (adjustmentValue == null) {
            throw new IllegalArgumentException("Adjustment value cannot be null");
        }
        if (adjustmentValue.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Adjustment value must be positive: " + adjustmentValue);
        }
        // Business rule: Extreme adjustments are not allowed (between 0.1 and 10.0)
        if (adjustmentValue.compareTo(BigDecimal.valueOf(0.1)) < 0 || 
            adjustmentValue.compareTo(BigDecimal.valueOf(10.0)) > 0) {
            throw new IllegalArgumentException(
                "Adjustment value must be between 0.1 and 10.0: " + adjustmentValue);
        }
    }
    
    private void validateDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            throw new IllegalArgumentException("Description cannot be null or empty");
        }
    }
    
    public PricingFactorType getType() {
        return type;
    }
    
    public BigDecimal getAdjustmentValue() {
        return adjustmentValue;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Checks if this factor increases the price (surcharge).
     * 
     * @return true if adjustment value > 1.0
     */
    public boolean isSurcharge() {
        return adjustmentValue.compareTo(BigDecimal.ONE) > 0;
    }
    
    /**
     * Checks if this factor decreases the price (discount).
     * 
     * @return true if adjustment value < 1.0
     */
    public boolean isDiscount() {
        return adjustmentValue.compareTo(BigDecimal.ONE) < 0;
    }
    
    /**
     * Checks if this factor has no effect on price.
     * 
     * @return true if adjustment value = 1.0
     */
    public boolean isNeutral() {
        return adjustmentValue.compareTo(BigDecimal.ONE) == 0;
    }
    
    /**
     * Gets the percentage change this factor represents.
     * 
     * @return Percentage change (positive for surcharge, negative for discount)
     */
    public BigDecimal getPercentageChange() {
        return adjustmentValue.subtract(BigDecimal.ONE).multiply(BigDecimal.valueOf(100));
    }
    
    /**
     * Factory methods for common pricing factors.
     */
    public static PricingFactor seasonalSurcharge(double percentage) {
        double multiplier = 1.0 + (percentage / 100.0);
        return new PricingFactor(PricingFactorType.SEASONAL, multiplier, 
                                String.format("Seasonal surcharge: %.1f%%", percentage));
    }
    
    public static PricingFactor advanceBookingDiscount(double percentage) {
        double multiplier = 1.0 - (percentage / 100.0);
        return new PricingFactor(PricingFactorType.ADVANCE_BOOKING, multiplier,
                                String.format("Advance booking discount: %.1f%%", percentage));
    }
    
    public static PricingFactor weekendSurcharge(double percentage) {
        double multiplier = 1.0 + (percentage / 100.0);
        return new PricingFactor(PricingFactorType.DAY_OF_WEEK, multiplier,
                                String.format("Weekend surcharge: %.1f%%", percentage));
    }
    
    public static PricingFactor loyaltyDiscount(double percentage) {
        double multiplier = 1.0 - (percentage / 100.0);
        return new PricingFactor(PricingFactorType.LOYALTY_STATUS, multiplier,
                                String.format("Loyalty member discount: %.1f%%", percentage));
    }
    
    public static PricingFactor demandSurcharge(double percentage) {
        double multiplier = 1.0 + (percentage / 100.0);
        return new PricingFactor(PricingFactorType.DEMAND_BASED, multiplier,
                                String.format("High demand surcharge: %.1f%%", percentage));
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PricingFactor that = (PricingFactor) obj;
        return Objects.equals(type, that.type) &&
               Objects.equals(adjustmentValue, that.adjustmentValue) &&
               Objects.equals(description, that.description);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(type, adjustmentValue, description);
    }
    
    @Override
    public String toString() {
        return String.format("PricingFactor{type=%s, adjustment=%.2f, description='%s'}", 
                           type, adjustmentValue, description);
    }
    
    /**
     * Enumeration of pricing factor types.
     */
    public enum PricingFactorType {
        SEASONAL("Seasonal pricing based on travel periods"),
        ADVANCE_BOOKING("Pricing based on how far in advance booking is made"),
        DAY_OF_WEEK("Pricing based on day of the week"),
        TIME_OF_DAY("Pricing based on departure/arrival time"),
        ROUTE_POPULARITY("Pricing based on route demand and popularity"),
        DEMAND_BASED("Dynamic pricing based on current demand"),
        LOYALTY_STATUS("Pricing adjustments for loyalty program members"),
        SEAT_CLASS("Pricing based on seat class (economy, business, first)"),
        AIRCRAFT_TYPE("Pricing based on aircraft type and amenities"),
        COMPETITION("Pricing adjustments based on competitor pricing");
        
        private final String description;
        
        PricingFactorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
