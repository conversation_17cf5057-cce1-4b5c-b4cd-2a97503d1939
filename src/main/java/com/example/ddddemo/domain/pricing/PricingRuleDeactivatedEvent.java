package com.example.ddddemo.domain.pricing;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.util.Objects;
import java.util.UUID;

/**
 * Domain Event published when a pricing rule is deactivated.
 */
public class PricingRuleDeactivatedEvent extends DomainEvent {
    
    private final UUID ruleId;
    private final String ruleName;
    
    public PricingRuleDeactivatedEvent(UUID ruleId, String ruleName) {
        super();
        this.ruleId = Objects.requireNonNull(ruleId, "Rule ID cannot be null");
        this.ruleName = Objects.requireNonNull(ruleName, "Rule name cannot be null");
    }
    
    public UUID getRuleId() {
        return ruleId;
    }
    
    public String getRuleName() {
        return ruleName;
    }
    
    @Override
    public String toString() {
        return String.format("PricingRuleDeactivatedEvent{ruleId=%s, ruleName='%s'}", ruleId, ruleName);
    }
}
