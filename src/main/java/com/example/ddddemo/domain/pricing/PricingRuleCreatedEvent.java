package com.example.ddddemo.domain.pricing;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Domain Event published when a new pricing rule is created.
 * 
 * This event represents the business fact that a new pricing rule
 * has been established in the system. Other bounded contexts might
 * be interested in this event, such as:
 * 
 * - Revenue Management: To analyze pricing strategy impact
 * - Analytics: To track pricing rule creation patterns
 * - Audit: To maintain a record of pricing changes
 * - Notification: To alert stakeholders of new pricing rules
 */
public class PricingRuleCreatedEvent extends DomainEvent {
    
    /**
     * The unique identifier of the created pricing rule.
     */
    private final UUID ruleId;
    
    /**
     * The name of the created pricing rule.
     */
    private final String ruleName;
    
    /**
     * The type of the created pricing rule.
     */
    private final PricingRule.PricingRuleType ruleType;
    
    /**
     * When the rule becomes effective.
     */
    private final LocalDateTime effectiveFrom;
    
    /**
     * Creates a new PricingRuleCreatedEvent.
     * 
     * @param ruleId The pricing rule identifier
     * @param ruleName The pricing rule name
     * @param ruleType The pricing rule type
     * @param effectiveFrom When the rule becomes effective
     */
    public PricingRuleCreatedEvent(UUID ruleId, String ruleName, 
                                  PricingRule.PricingRuleType ruleType, 
                                  LocalDateTime effectiveFrom) {
        super();
        this.ruleId = Objects.requireNonNull(ruleId, "Rule ID cannot be null");
        this.ruleName = Objects.requireNonNull(ruleName, "Rule name cannot be null");
        this.ruleType = Objects.requireNonNull(ruleType, "Rule type cannot be null");
        this.effectiveFrom = Objects.requireNonNull(effectiveFrom, "Effective from cannot be null");
    }
    
    public UUID getRuleId() {
        return ruleId;
    }
    
    public String getRuleName() {
        return ruleName;
    }
    
    public PricingRule.PricingRuleType getRuleType() {
        return ruleType;
    }
    
    public LocalDateTime getEffectiveFrom() {
        return effectiveFrom;
    }
    
    @Override
    public String toString() {
        return String.format("PricingRuleCreatedEvent{ruleId=%s, ruleName='%s', ruleType=%s, effectiveFrom=%s}", 
                           ruleId, ruleName, ruleType, effectiveFrom);
    }
}
