package com.example.ddddemo.domain.pricing.specifications;

import com.example.ddddemo.domain.shared.Specification;
import com.example.ddddemo.domain.flight.Route;
import com.example.ddddemo.domain.flight.AirportCode;
import java.util.Set;

/**
 * Specification that determines if a route is considered popular.
 * 
 * Popular routes typically have higher demand and can command premium pricing.
 * This specification encapsulates the business logic for identifying popular routes
 * based on various criteria such as hub airports, major city pairs, and
 * high-traffic corridors.
 * 
 * Business Rule: A route is considered popular if:
 * 1. It connects two major hub airports, OR
 * 2. It's part of a predefined list of high-demand routes, OR
 * 3. It connects major business centers
 * 
 * This demonstrates:
 * - Complex business rule encapsulation
 * - Use of predefined business data
 * - Route-specific business logic
 * 
 * Usage Example:
 * ```java
 * Specification<Route> popularRouteSpec = new IsPopularRouteSpecification();
 * if (popularRouteSpec.isSatisfiedBy(route)) {
 *     // Apply popular route surcharge
 * }
 * ```
 */
public class IsPopularRouteSpecification implements Specification<Route> {
    
    /**
     * Set of popular route codes that are known to have high demand.
     * In a real system, this would likely be loaded from a database or configuration.
     */
    private static final Set<String> POPULAR_ROUTE_CODES = Set.of(
        // Major US transcontinental routes
        "JFK-LAX", "LAX-JFK",  // New York to Los Angeles
        "JFK-SFO", "SFO-JFK",  // New York to San Francisco
        "ORD-LAX", "LAX-ORD",  // Chicago to Los Angeles
        "DFW-LAX", "LAX-DFW",  // Dallas to Los Angeles
        
        // Major international routes
        "JFK-LHR", "LHR-JFK",  // New York to London
        "LAX-LHR", "LHR-LAX",  // Los Angeles to London
        "JFK-CDG", "CDG-JFK",  // New York to Paris
        "ORD-LHR", "LHR-ORD",  // Chicago to London
        
        // Major business routes
        "JFK-ORD", "ORD-JFK",  // New York to Chicago
        "LAX-SFO", "SFO-LAX",  // Los Angeles to San Francisco
        "DFW-ORD", "ORD-DFW",  // Dallas to Chicago
        "ATL-JFK", "JFK-ATL"   // Atlanta to New York
    );
    
    /**
     * Set of major business center airport codes.
     */
    private static final Set<String> MAJOR_BUSINESS_CENTERS = Set.of(
        "JFK", "LGA", "EWR",  // New York area
        "LAX", "SFO",         // California major cities
        "ORD", "MDW",         // Chicago area
        "DFW", "IAH",         // Texas major hubs
        "ATL",                // Atlanta hub
        "LHR", "LGW",         // London area
        "CDG", "ORY",         // Paris area
        "DEN", "PHX"          // Mountain/Southwest hubs
    );
    
    @Override
    public boolean isSatisfiedBy(Route route) {
        if (route == null) {
            return false;
        }
        
        // Check if it's a predefined popular route
        if (isPredefinedPopularRoute(route)) {
            return true;
        }
        
        // Check if it connects two major hub airports
        if (connectsHubAirports(route)) {
            return true;
        }
        
        // Check if it connects major business centers
        if (connectsBusinessCenters(route)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Checks if the route is in the predefined list of popular routes.
     */
    private boolean isPredefinedPopularRoute(Route route) {
        String routeCode = route.getRouteCode();
        return POPULAR_ROUTE_CODES.contains(routeCode);
    }
    
    /**
     * Checks if the route connects two major hub airports.
     */
    private boolean connectsHubAirports(Route route) {
        return route.getOrigin().isMajorHub() && route.getDestination().isMajorHub();
    }
    
    /**
     * Checks if the route connects major business centers.
     */
    private boolean connectsBusinessCenters(Route route) {
        String originCode = route.getOrigin().getCode();
        String destinationCode = route.getDestination().getCode();
        
        return MAJOR_BUSINESS_CENTERS.contains(originCode) && 
               MAJOR_BUSINESS_CENTERS.contains(destinationCode);
    }
    
    /**
     * Checks if the route is a transcontinental route within the US.
     * These routes typically have high demand due to business travel.
     * 
     * @param route The route to check
     * @return true if the route is transcontinental
     */
    public boolean isTranscontinentalRoute(Route route) {
        if (route == null || !route.isDomestic()) {
            return false;
        }
        
        // Simplified logic: East coast to West coast
        String originCode = route.getOrigin().getCode();
        String destinationCode = route.getDestination().getCode();
        
        Set<String> eastCoastAirports = Set.of("JFK", "LGA", "EWR", "BOS", "DCA", "IAD", "BWI", "PHL", "ATL", "MIA");
        Set<String> westCoastAirports = Set.of("LAX", "SFO", "SAN", "SEA", "PDX", "LAS", "PHX", "DEN");
        
        return (eastCoastAirports.contains(originCode) && westCoastAirports.contains(destinationCode)) ||
               (westCoastAirports.contains(originCode) && eastCoastAirports.contains(destinationCode));
    }
    
    /**
     * Checks if the route is a major international route.
     * 
     * @param route The route to check
     * @return true if the route is a major international route
     */
    public boolean isMajorInternationalRoute(Route route) {
        if (route == null || route.isDomestic()) {
            return false;
        }
        
        // Check if it connects major international gateways
        String originCode = route.getOrigin().getCode();
        String destinationCode = route.getDestination().getCode();
        
        Set<String> internationalGateways = Set.of(
            "JFK", "LAX", "SFO", "ORD", "DFW", "ATL",  // US gateways
            "LHR", "CDG", "FRA", "AMS", "ZUR",         // European gateways
            "NRT", "ICN", "PVG", "HKG", "SIN"          // Asian gateways
        );
        
        return internationalGateways.contains(originCode) && internationalGateways.contains(destinationCode);
    }
    
    /**
     * Gets the popularity tier of the route.
     * This can be used for graduated pricing strategies.
     * 
     * @param route The route to evaluate
     * @return Popularity tier (1 = highest, 3 = lowest, 0 = not popular)
     */
    public int getPopularityTier(Route route) {
        if (!isSatisfiedBy(route)) {
            return 0;
        }
        
        if (isPredefinedPopularRoute(route)) {
            return 1; // Highest tier
        }
        
        if (isTranscontinentalRoute(route) || isMajorInternationalRoute(route)) {
            return 2; // High tier
        }
        
        if (connectsHubAirports(route) || connectsBusinessCenters(route)) {
            return 3; // Medium tier
        }
        
        return 0;
    }
    
    /**
     * Gets the suggested surcharge percentage based on route popularity.
     * 
     * @param route The route to evaluate
     * @return Suggested surcharge percentage
     */
    public double getSuggestedSurchargePercentage(Route route) {
        int tier = getPopularityTier(route);
        
        return switch (tier) {
            case 1 -> 30.0; // 30% surcharge for top-tier routes
            case 2 -> 20.0; // 20% surcharge for high-tier routes
            case 3 -> 10.0; // 10% surcharge for medium-tier routes
            default -> 0.0; // No surcharge for non-popular routes
        };
    }
}
