package com.example.ddddemo.domain.pricing;

import com.example.ddddemo.domain.shared.ValueObject;
import java.util.Objects;
import java.util.UUID;

/**
 * PriceImpactAnalysis Value Object for analyzing the impact of pricing rule changes.
 * 
 * This is a simplified placeholder for demonstration purposes.
 * In a real system, this would contain detailed analysis of how
 * a pricing rule change affects overall pricing across different routes,
 * customer segments, and time periods.
 */
public class PriceImpactAnalysis implements ValueObject {
    
    private final UUID ruleId;
    private final String analysisResult;
    
    public PriceImpactAnalysis(UUID ruleId, String analysisResult) {
        this.ruleId = Objects.requireNonNull(ruleId, "Rule ID cannot be null");
        this.analysisResult = Objects.requireNonNull(analysisResult, "Analysis result cannot be null");
    }
    
    public UUID getRuleId() {
        return ruleId;
    }
    
    public String getAnalysisResult() {
        return analysisResult;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PriceImpactAnalysis that = (PriceImpactAnalysis) obj;
        return Objects.equals(ruleId, that.ruleId) &&
               Objects.equals(analysisResult, that.analysisResult);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(ruleId, analysisResult);
    }
    
    @Override
    public String toString() {
        return String.format("PriceImpactAnalysis{ruleId=%s, result='%s'}", ruleId, analysisResult);
    }
}
