package com.example.ddddemo.domain.shared;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Currency;
import java.util.Objects;

/**
 * Money Value Object representing monetary amounts in our aviation pricing system.
 * 
 * This is a classic example of a Value Object in DDD. Money is immutable,
 * has no identity, and equality is based on its attributes (amount and currency).
 * 
 * Key design decisions:
 * 1. Uses BigDecimal for precise decimal arithmetic (avoiding floating-point errors)
 * 2. Includes currency to prevent mixing different currencies
 * 3. Immutable - all operations return new Money instances
 * 4. Validates business rules (no negative amounts for prices)
 * 5. Provides meaningful business operations (add, subtract, multiply)
 * 
 * This demonstrates several DDD principles:
 * - Value Objects should be immutable
 * - Business logic belongs in the domain model
 * - Domain concepts should be explicitly modeled
 * - Validation should happen at construction time
 */
public class Money implements ValueObject, Comparable<Money> {
    
    /**
     * The monetary amount using BigDecimal for precision.
     * BigDecimal is essential for financial calculations to avoid
     * floating-point precision errors.
     */
    private final BigDecimal amount;
    
    /**
     * The currency of this monetary amount.
     * Using Java's Currency class ensures valid currency codes.
     */
    private final Currency currency;
    
    /**
     * Scale for monetary calculations (2 decimal places for most currencies).
     */
    private static final int SCALE = 2;
    
    /**
     * Rounding mode for monetary calculations.
     * HALF_UP is commonly used in financial systems.
     */
    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;
    
    /**
     * Creates a new Money instance.
     * 
     * @param amount The monetary amount (must not be null or negative)
     * @param currency The currency (must not be null)
     * @throws IllegalArgumentException if amount is null, negative, or currency is null
     */
    public Money(BigDecimal amount, Currency currency) {
        validateAmount(amount);
        validateCurrency(currency);
        
        this.amount = amount.setScale(SCALE, ROUNDING_MODE);
        this.currency = currency;
    }
    
    /**
     * Convenience constructor for creating Money from double values.
     * Note: Use with caution due to floating-point precision issues.
     * Prefer the BigDecimal constructor for precise amounts.
     * 
     * @param amount The monetary amount as double
     * @param currency The currency
     */
    public Money(double amount, Currency currency) {
        this(BigDecimal.valueOf(amount), currency);
    }
    
    /**
     * Convenience constructor for creating Money with string amounts.
     * 
     * @param amount The monetary amount as string
     * @param currency The currency
     */
    public Money(String amount, Currency currency) {
        this(new BigDecimal(amount), currency);
    }
    
    /**
     * Factory method for creating USD money.
     * 
     * @param amount The amount in USD
     * @return Money instance in USD
     */
    public static Money usd(BigDecimal amount) {
        return new Money(amount, Currency.getInstance("USD"));
    }
    
    /**
     * Factory method for creating USD money from double.
     * 
     * @param amount The amount in USD
     * @return Money instance in USD
     */
    public static Money usd(double amount) {
        return new Money(amount, Currency.getInstance("USD"));
    }
    
    /**
     * Factory method for zero money in given currency.
     * 
     * @param currency The currency
     * @return Zero money in the specified currency
     */
    public static Money zero(Currency currency) {
        return new Money(BigDecimal.ZERO, currency);
    }
    
    private void validateAmount(BigDecimal amount) {
        if (amount == null) {
            throw new IllegalArgumentException("Amount cannot be null");
        }
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Amount cannot be negative: " + amount);
        }
    }
    
    private void validateCurrency(Currency currency) {
        if (currency == null) {
            throw new IllegalArgumentException("Currency cannot be null");
        }
    }
    
    /**
     * Gets the monetary amount.
     * 
     * @return The amount as BigDecimal
     */
    public BigDecimal getAmount() {
        return amount;
    }
    
    /**
     * Gets the currency.
     * 
     * @return The currency
     */
    public Currency getCurrency() {
        return currency;
    }
    
    /**
     * Adds another Money amount to this one.
     * Both amounts must be in the same currency.
     * 
     * @param other The money to add
     * @return New Money instance with the sum
     * @throws IllegalArgumentException if currencies don't match
     */
    public Money add(Money other) {
        validateSameCurrency(other);
        return new Money(this.amount.add(other.amount), this.currency);
    }
    
    /**
     * Subtracts another Money amount from this one.
     * Both amounts must be in the same currency.
     * 
     * @param other The money to subtract
     * @return New Money instance with the difference
     * @throws IllegalArgumentException if currencies don't match or result would be negative
     */
    public Money subtract(Money other) {
        validateSameCurrency(other);
        BigDecimal result = this.amount.subtract(other.amount);
        return new Money(result, this.currency);
    }
    
    /**
     * Multiplies this money by a factor.
     * 
     * @param factor The multiplication factor
     * @return New Money instance with the product
     */
    public Money multiply(BigDecimal factor) {
        if (factor == null) {
            throw new IllegalArgumentException("Factor cannot be null");
        }
        return new Money(this.amount.multiply(factor), this.currency);
    }
    
    /**
     * Multiplies this money by a percentage.
     * 
     * @param percentage The percentage (e.g., 15 for 15%)
     * @return New Money instance with the result
     */
    public Money multiplyByPercentage(double percentage) {
        BigDecimal factor = BigDecimal.valueOf(percentage).divide(BigDecimal.valueOf(100), SCALE + 2, ROUNDING_MODE);
        return multiply(factor);
    }
    
    /**
     * Checks if this money amount is zero.
     * 
     * @return true if amount is zero
     */
    public boolean isZero() {
        return amount.compareTo(BigDecimal.ZERO) == 0;
    }
    
    /**
     * Checks if this money amount is positive.
     * 
     * @return true if amount is greater than zero
     */
    public boolean isPositive() {
        return amount.compareTo(BigDecimal.ZERO) > 0;
    }
    
    private void validateSameCurrency(Money other) {
        if (!this.currency.equals(other.currency)) {
            throw new IllegalArgumentException(
                String.format("Cannot operate on different currencies: %s and %s", 
                            this.currency, other.currency));
        }
    }
    
    @Override
    public int compareTo(Money other) {
        validateSameCurrency(other);
        return this.amount.compareTo(other.amount);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Money money = (Money) obj;
        return Objects.equals(amount, money.amount) && 
               Objects.equals(currency, money.currency);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(amount, currency);
    }
    
    @Override
    public String toString() {
        return String.format("%s %s", currency.getCurrencyCode(), amount);
    }
}
