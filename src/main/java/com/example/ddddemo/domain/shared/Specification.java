package com.example.ddddemo.domain.shared;

/**
 * Interface for the Specification pattern in our DDD implementation.
 * 
 * The Specification pattern is used to encapsulate business rules and criteria
 * in a reusable, composable way. It allows complex business logic to be
 * expressed clearly and combined using logical operators.
 * 
 * Key benefits of the Specification pattern:
 * 1. Encapsulation: Business rules are contained in dedicated classes
 * 2. Reusability: Specifications can be reused across different contexts
 * 3. Composability: Complex rules can be built from simpler ones
 * 4. Testability: Each specification can be tested independently
 * 5. Expressiveness: Business rules are clearly named and documented
 * 
 * Common use cases in our aviation domain:
 * - Eligibility rules: IsEligibleForAdvanceBookingDiscount
 * - Validation rules: IsValidFlightRoute
 * - Business criteria: IsPopularRoute, IsWeekendFlight
 * - Pricing rules: HasLoyaltyMembership, IsHighDemandPeriod
 * 
 * Example usage:
 * 
 * // Simple specifications
 * Specification<Booking> advanceBooking = new IsAdvanceBookingSpecification();
 * Specification<Customer> loyaltyMember = new IsLoyaltyMemberSpecification();
 * 
 * // Composed specifications
 * Specification<Booking> discountEligible = advanceBooking.and(loyaltyMember);
 * 
 * // Using specifications
 * if (discountEligible.isSatisfiedBy(booking)) {
 *     // Apply discount logic
 * }
 * 
 * @param <T> The type of object this specification evaluates
 */
public interface Specification<T> {
    
    /**
     * Checks if the given candidate satisfies this specification.
     * 
     * @param candidate The object to evaluate against this specification
     * @return true if the candidate satisfies the specification, false otherwise
     */
    boolean isSatisfiedBy(T candidate);
    
    /**
     * Creates a new specification that is the logical AND of this specification
     * and the given specification.
     * 
     * @param other The specification to combine with this one using AND logic
     * @return A new specification representing the AND combination
     */
    default Specification<T> and(Specification<T> other) {
        return new AndSpecification<>(this, other);
    }
    
    /**
     * Creates a new specification that is the logical OR of this specification
     * and the given specification.
     * 
     * @param other The specification to combine with this one using OR logic
     * @return A new specification representing the OR combination
     */
    default Specification<T> or(Specification<T> other) {
        return new OrSpecification<>(this, other);
    }
    
    /**
     * Creates a new specification that is the logical NOT of this specification.
     * 
     * @return A new specification representing the NOT of this specification
     */
    default Specification<T> not() {
        return new NotSpecification<>(this);
    }
    
    /**
     * Implementation of the AND logical operator for specifications.
     */
    class AndSpecification<T> implements Specification<T> {
        private final Specification<T> left;
        private final Specification<T> right;
        
        public AndSpecification(Specification<T> left, Specification<T> right) {
            this.left = left;
            this.right = right;
        }
        
        @Override
        public boolean isSatisfiedBy(T candidate) {
            return left.isSatisfiedBy(candidate) && right.isSatisfiedBy(candidate);
        }
    }
    
    /**
     * Implementation of the OR logical operator for specifications.
     */
    class OrSpecification<T> implements Specification<T> {
        private final Specification<T> left;
        private final Specification<T> right;
        
        public OrSpecification(Specification<T> left, Specification<T> right) {
            this.left = left;
            this.right = right;
        }
        
        @Override
        public boolean isSatisfiedBy(T candidate) {
            return left.isSatisfiedBy(candidate) || right.isSatisfiedBy(candidate);
        }
    }
    
    /**
     * Implementation of the NOT logical operator for specifications.
     */
    class NotSpecification<T> implements Specification<T> {
        private final Specification<T> specification;
        
        public NotSpecification(Specification<T> specification) {
            this.specification = specification;
        }
        
        @Override
        public boolean isSatisfiedBy(T candidate) {
            return !specification.isSatisfiedBy(candidate);
        }
    }
}
