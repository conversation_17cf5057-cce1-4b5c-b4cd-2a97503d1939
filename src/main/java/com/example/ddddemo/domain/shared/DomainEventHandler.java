package com.example.ddddemo.domain.shared;

/**
 * Interface for handling domain events.
 * 
 * Domain Event Handlers are responsible for processing domain events and
 * implementing the side effects that should occur when specific business
 * events happen in the system.
 * 
 * Key characteristics of Domain Event Handlers:
 * - Stateless: Handlers should not maintain state between invocations
 * - Idempotent: Handlers should be able to process the same event multiple times safely
 * - Fast: Handlers should complete quickly to avoid blocking other operations
 * - Focused: Each handler should have a single responsibility
 * 
 * Types of Domain Event Handlers:
 * 1. Integration Handlers: Send events to external systems
 * 2. Projection Handlers: Update read models and views
 * 3. Notification Handlers: Send emails, SMS, push notifications
 * 4. Audit Handlers: Log events for compliance and debugging
 * 5. Saga Handlers: Coordinate complex business processes
 * 6. Cache Handlers: Invalidate or update caches
 * 
 * Error Handling Strategies:
 * - Retry: Automatically retry failed event processing
 * - Dead Letter: Move failed events to a dead letter queue
 * - Compensation: Execute compensating actions for failures
 * - Circuit Breaker: Stop processing if too many failures occur
 * 
 * Usage Example:
 * ```java
 * @Component
 * public class BookingConfirmedHandler implements DomainEventHandler<BookingConfirmedEvent> {
 *     
 *     private final EmailService emailService;
 *     private final FlightService flightService;
 *     
 *     @Override
 *     public void handle(BookingConfirmedEvent event) {
 *         // Send confirmation email
 *         emailService.sendBookingConfirmation(event.getBookingReference());
 *         
 *         // Reserve seats on the flight
 *         flightService.reserveSeats(event.getFlightId(), event.getPassengerCount());
 *     }
 *     
 *     @Override
 *     public boolean canHandle(DomainEvent event) {
 *         return event instanceof BookingConfirmedEvent;
 *     }
 * }
 * ```
 * 
 * @param <T> The type of domain event this handler processes
 */
public interface DomainEventHandler<T extends DomainEvent> {
    
    /**
     * Handles the domain event.
     * 
     * This method contains the business logic that should be executed
     * when the specific domain event occurs. Implementations should:
     * - Be idempotent (safe to call multiple times with same event)
     * - Complete quickly to avoid blocking other operations
     * - Handle exceptions gracefully
     * - Log important information for debugging
     * 
     * @param event The domain event to handle
     * @throws DomainEventHandlingException if the event cannot be processed
     */
    void handle(T event) throws DomainEventHandlingException;
    
    /**
     * Checks if this handler can process the given domain event.
     * 
     * This method allows for dynamic event routing and filtering.
     * The default implementation checks if the event is an instance
     * of the handler's generic type parameter.
     * 
     * @param event The domain event to check
     * @return true if this handler can process the event
     */
    default boolean canHandle(DomainEvent event) {
        // This would need to be implemented using reflection or
        // overridden in concrete implementations
        return true;
    }
    
    /**
     * Gets the priority of this handler.
     * 
     * When multiple handlers are registered for the same event type,
     * they will be executed in priority order (higher numbers first).
     * This allows for controlling the order of side effects.
     * 
     * @return The handler priority (default is 0)
     */
    default int getPriority() {
        return 0;
    }
    
    /**
     * Checks if this handler should be executed asynchronously.
     * 
     * Asynchronous handlers are executed in background threads and
     * don't block the main business operation. This is useful for
     * non-critical side effects like sending notifications.
     * 
     * @return true if the handler should be executed asynchronously
     */
    default boolean isAsync() {
        return false;
    }
    
    /**
     * Gets the maximum number of retry attempts for this handler.
     * 
     * If event processing fails, the system can automatically retry
     * the operation up to the specified number of times.
     * 
     * @return Maximum retry attempts (default is 3)
     */
    default int getMaxRetryAttempts() {
        return 3;
    }
    
    /**
     * Gets the retry delay in milliseconds.
     * 
     * This is the delay between retry attempts when event processing fails.
     * 
     * @return Retry delay in milliseconds (default is 1000ms)
     */
    default long getRetryDelayMs() {
        return 1000L;
    }
    
    /**
     * Called when event processing fails after all retry attempts.
     * 
     * This method allows handlers to implement custom error handling
     * logic, such as logging, alerting, or moving events to a dead
     * letter queue.
     * 
     * @param event The event that failed to process
     * @param exception The exception that caused the failure
     */
    default void onFailure(T event, Exception exception) {
        // Default implementation does nothing
        // Concrete handlers can override for custom error handling
    }
    
    /**
     * Called before event processing begins.
     * 
     * This method can be used for setup, logging, or validation.
     * 
     * @param event The event about to be processed
     */
    default void beforeHandle(T event) {
        // Default implementation does nothing
    }
    
    /**
     * Called after successful event processing.
     * 
     * This method can be used for cleanup, logging, or metrics collection.
     * 
     * @param event The event that was successfully processed
     */
    default void afterHandle(T event) {
        // Default implementation does nothing
    }
    
    /**
     * Gets a human-readable name for this handler.
     * 
     * This is useful for logging, monitoring, and debugging.
     * 
     * @return The handler name (defaults to class simple name)
     */
    default String getHandlerName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * Checks if this handler is currently enabled.
     * 
     * Disabled handlers will be skipped during event processing.
     * This can be useful for feature flags or maintenance scenarios.
     * 
     * @return true if the handler is enabled (default is true)
     */
    default boolean isEnabled() {
        return true;
    }
}
