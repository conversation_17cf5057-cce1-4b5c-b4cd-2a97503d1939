package com.example.ddddemo.domain.shared;

/**
 * Marker interface for Value Objects in our DDD implementation.
 * 
 * In Domain Driven Design, a Value Object is an object that describes some
 * characteristic or attribute but carries no concept of identity. Value Objects
 * are immutable and their equality is based on their attributes, not identity.
 * 
 * Key characteristics of Value Objects:
 * - Immutable: Once created, they cannot be changed
 * - No identity: Two value objects are equal if all their attributes are equal
 * - Side-effect free: Methods should not modify the object's state
 * - Replaceable: If you need to change a value object, create a new one
 * 
 * Examples in our aviation domain:
 * - Money: Represents an amount and currency (e.g., $150.00 USD)
 * - FlightNumber: Represents a flight identifier (e.g., "AA1234")
 * - AirportCode: Represents an airport (e.g., "JFK", "LAX")
 * - Route: Represents a path between two airports
 * - Address: Represents a physical location
 * 
 * Implementation Guidelines:
 * 1. Make all fields final and private
 * 2. Initialize all fields in the constructor
 * 3. Provide no setters, only getters
 * 4. Override equals() and hashCode() based on all attributes
 * 5. Consider implementing Comparable if natural ordering exists
 * 6. Validate invariants in the constructor
 * 
 * Example implementation:
 * 
 * public class Money implements ValueObject {
 *     private final BigDecimal amount;
 *     private final Currency currency;
 *     
 *     public Money(BigDecimal amount, Currency currency) {
 *         if (amount == null || currency == null) {
 *             throw new IllegalArgumentException("Amount and currency cannot be null");
 *         }
 *         if (amount.compareTo(BigDecimal.ZERO) < 0) {
 *             throw new IllegalArgumentException("Amount cannot be negative");
 *         }
 *         this.amount = amount;
 *         this.currency = currency;
 *     }
 *     
 *     // Getters only, no setters
 *     public BigDecimal getAmount() { return amount; }
 *     public Currency getCurrency() { return currency; }
 *     
 *     // Business methods that return new instances
 *     public Money add(Money other) {
 *         if (!this.currency.equals(other.currency)) {
 *             throw new IllegalArgumentException("Cannot add different currencies");
 *         }
 *         return new Money(this.amount.add(other.amount), this.currency);
 *     }
 *     
 *     @Override
 *     public boolean equals(Object obj) {
 *         if (this == obj) return true;
 *         if (obj == null || getClass() != obj.getClass()) return false;
 *         Money money = (Money) obj;
 *         return Objects.equals(amount, money.amount) && 
 *                Objects.equals(currency, money.currency);
 *     }
 *     
 *     @Override
 *     public int hashCode() {
 *         return Objects.hash(amount, currency);
 *     }
 * }
 */
public interface ValueObject {
    // Marker interface - no methods required
    // Implementing classes should follow the value object principles
}
