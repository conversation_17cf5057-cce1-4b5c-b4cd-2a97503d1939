package com.example.ddddemo.domain.shared;

import java.util.Objects;

/**
 * Base class for all Domain Entities in our DDD implementation.
 * 
 * In Domain Driven Design, an Entity is an object that has a distinct identity
 * that runs through time and different representations. Two entities are equal
 * if they have the same identity, regardless of their other attributes.
 * 
 * Key characteristics of Entities:
 * - Have a unique identity (ID)
 * - Identity remains constant throughout the entity's lifecycle
 * - Equality is based on identity, not attributes
 * - Can change their attributes over time while maintaining identity
 * 
 * Examples in our aviation domain:
 * - Flight: Each flight has a unique flight number and schedule
 * - Aircraft: Each aircraft has a unique registration number
 * - Passenger: Each passenger has a unique passenger ID
 * 
 * @param <ID> The type of the entity's identifier
 */
public abstract class Entity<ID> {
    
    /**
     * The unique identifier for this entity.
     * This should be immutable once set and never null for persisted entities.
     */
    protected ID id;
    
    /**
     * Protected constructor to prevent direct instantiation.
     * Entities should be created through factories or constructors in concrete classes.
     */
    protected Entity() {
    }
    
    /**
     * Constructor with ID for creating entities with known identifiers.
     * 
     * @param id The unique identifier for this entity
     */
    protected Entity(ID id) {
        this.id = id;
    }
    
    /**
     * Gets the unique identifier of this entity.
     * 
     * @return The entity's ID, may be null for new entities not yet persisted
     */
    public ID getId() {
        return id;
    }
    
    /**
     * Checks if this entity has been assigned an ID.
     * Useful for determining if an entity is new or has been persisted.
     * 
     * @return true if the entity has an ID, false otherwise
     */
    public boolean hasId() {
        return id != null;
    }
    
    /**
     * Two entities are equal if they have the same type and the same ID.
     * If either entity doesn't have an ID, they are not equal.
     * This follows the DDD principle that entity equality is based on identity.
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        
        Entity<?> other = (Entity<?>) obj;
        
        // Entities without IDs are never equal to anything (including themselves)
        // This prevents issues with new entities before they're persisted
        if (this.id == null || other.id == null) {
            return false;
        }
        
        return Objects.equals(this.id, other.id);
    }
    
    /**
     * Hash code is based on the entity's ID.
     * If the entity doesn't have an ID, use the default Object hashCode.
     */
    @Override
    public int hashCode() {
        if (id == null) {
            return super.hashCode();
        }
        return Objects.hash(getClass(), id);
    }
    
    /**
     * String representation includes the class name and ID.
     * Useful for debugging and logging.
     */
    @Override
    public String toString() {
        return String.format("%s{id=%s}", getClass().getSimpleName(), id);
    }
}
