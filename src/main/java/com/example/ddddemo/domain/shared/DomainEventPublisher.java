package com.example.ddddemo.domain.shared;

import java.util.List;

/**
 * Domain Event Publisher interface for publishing domain events.
 * 
 * The Domain Event Publisher is responsible for dispatching domain events
 * to interested subscribers. This enables loose coupling between aggregates
 * and supports eventual consistency across bounded contexts.
 * 
 * Key responsibilities:
 * - Publish domain events to registered handlers
 * - Support both synchronous and asynchronous event processing
 * - Ensure reliable event delivery
 * - Provide event ordering guarantees when needed
 * 
 * This interface belongs to the domain layer because:
 * - It defines the contract for domain event publishing
 * - It's used by aggregate roots to publish events
 * - The implementation belongs to the infrastructure layer
 * 
 * Benefits of Domain Events:
 * 1. Decouple aggregates: Aggregates don't need direct references
 * 2. Enable eventual consistency: Changes propagate asynchronously
 * 3. Support integration: External systems can subscribe to events
 * 4. Audit trail: Events provide natural audit logging
 * 5. Enable CQRS: Events can update read models
 * 6. Support sagas: Complex business processes across aggregates
 * 
 * Event Processing Patterns:
 * - Immediate: Events processed synchronously in the same transaction
 * - Deferred: Events processed after transaction commit
 * - Asynchronous: Events processed in background threads/processes
 * - Persistent: Events stored for replay and recovery
 * 
 * Usage Example:
 * ```java
 * // In an aggregate root
 * public void processPayment(Money amount) {
 *     // Business logic
 *     this.paymentStatus = PaymentStatus.COMPLETED;
 *     
 *     // Publish domain event
 *     addDomainEvent(new PaymentProcessedEvent(this.id, amount));
 * }
 * 
 * // In application service
 * public void handleBookingCreation(CreateBookingCommand command) {
 *     Booking booking = new Booking(...);
 *     bookingRepository.save(booking);
 *     
 *     // Publish events from the aggregate
 *     domainEventPublisher.publishEvents(booking.getDomainEvents());
 *     booking.clearDomainEvents();
 * }
 * ```
 */
public interface DomainEventPublisher {
    
    /**
     * Publishes a single domain event.
     * 
     * @param event The domain event to publish
     */
    void publishEvent(DomainEvent event);
    
    /**
     * Publishes multiple domain events.
     * Events are typically published in the order they were added to the aggregate.
     * 
     * @param events The list of domain events to publish
     */
    void publishEvents(List<DomainEvent> events);
    
    /**
     * Publishes events from an aggregate root.
     * This is a convenience method that extracts and publishes events from an aggregate.
     * 
     * @param aggregateRoot The aggregate root containing events to publish
     */
    default void publishEventsFrom(AggregateRoot<?> aggregateRoot) {
        if (aggregateRoot.hasDomainEvents()) {
            publishEvents(aggregateRoot.getDomainEvents());
            aggregateRoot.clearDomainEvents();
        }
    }
    
    /**
     * Publishes an event asynchronously.
     * The event will be processed in a background thread or process.
     * 
     * @param event The domain event to publish asynchronously
     */
    void publishEventAsync(DomainEvent event);
    
    /**
     * Publishes multiple events asynchronously.
     * 
     * @param events The list of domain events to publish asynchronously
     */
    void publishEventsAsync(List<DomainEvent> events);
    
    /**
     * Registers a domain event handler for a specific event type.
     * This allows for dynamic registration of event handlers.
     * 
     * @param eventType The type of event to handle
     * @param handler The event handler
     * @param <T> The event type
     */
    <T extends DomainEvent> void registerHandler(Class<T> eventType, DomainEventHandler<T> handler);
    
    /**
     * Unregisters a domain event handler for a specific event type.
     * 
     * @param eventType The type of event to stop handling
     * @param handler The event handler to remove
     * @param <T> The event type
     */
    <T extends DomainEvent> void unregisterHandler(Class<T> eventType, DomainEventHandler<T> handler);
    
    /**
     * Checks if there are any registered handlers for the given event type.
     * 
     * @param eventType The event type to check
     * @return true if there are registered handlers
     */
    boolean hasHandlersFor(Class<? extends DomainEvent> eventType);
    
    /**
     * Gets the number of registered handlers for the given event type.
     * 
     * @param eventType The event type to check
     * @return Number of registered handlers
     */
    int getHandlerCount(Class<? extends DomainEvent> eventType);
    
    /**
     * Publishes an event and waits for all handlers to complete processing.
     * This is useful when you need to ensure all side effects are completed
     * before continuing with the business operation.
     * 
     * @param event The domain event to publish
     * @param timeoutMillis Maximum time to wait for handlers to complete
     * @return true if all handlers completed within the timeout
     */
    boolean publishEventAndWait(DomainEvent event, long timeoutMillis);
    
    /**
     * Enables or disables event publishing.
     * This can be useful for testing or maintenance scenarios.
     * 
     * @param enabled Whether event publishing should be enabled
     */
    void setPublishingEnabled(boolean enabled);
    
    /**
     * Checks if event publishing is currently enabled.
     * 
     * @return true if event publishing is enabled
     */
    boolean isPublishingEnabled();
    
    /**
     * Gets statistics about event publishing.
     * This can be useful for monitoring and debugging.
     * 
     * @return Event publishing statistics
     */
    EventPublishingStats getStats();
    
    /**
     * Statistics about domain event publishing.
     */
    interface EventPublishingStats {
        /**
         * Gets the total number of events published.
         */
        long getTotalEventsPublished();
        
        /**
         * Gets the number of events published successfully.
         */
        long getSuccessfulEvents();
        
        /**
         * Gets the number of events that failed to publish.
         */
        long getFailedEvents();
        
        /**
         * Gets the average event processing time in milliseconds.
         */
        double getAverageProcessingTimeMs();
        
        /**
         * Gets the number of currently registered handlers.
         */
        int getRegisteredHandlerCount();
        
        /**
         * Resets all statistics counters.
         */
        void reset();
    }
}
