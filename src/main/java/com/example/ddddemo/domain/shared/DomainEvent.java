package com.example.ddddemo.domain.shared;

import java.time.Instant;
import java.util.Objects;
import java.util.UUID;

/**
 * Base class for Domain Events in our DDD implementation.
 * 
 * Domain Events represent something important that happened in the domain
 * that other parts of the system might be interested in. They enable
 * loose coupling between different bounded contexts and aggregates.
 * 
 * Key characteristics of Domain Events:
 * - Immutable: Once created, they cannot be changed
 * - Past tense naming: They represent something that already happened
 * - Rich in domain information: They carry relevant business data
 * - Timestamped: They record when the event occurred
 * - Uniquely identified: Each event has a unique ID
 * 
 * Benefits of Domain Events:
 * 1. Decouple aggregates: Aggregates don't need direct references to each other
 * 2. Enable eventual consistency: Changes can be propagated asynchronously
 * 3. Support integration: External systems can subscribe to relevant events
 * 4. Audit trail: Events provide a natural audit log of what happened
 * 5. Enable CQRS: Events can update read models independently
 * 
 * Examples in our aviation domain:
 * - FlightScheduled: When a new flight is added to the schedule
 * - PricingRuleUpdated: When pricing rules change
 * - BookingCreated: When a customer makes a booking
 * - PaymentProcessed: When payment is successfully processed
 * - SeatAssigned: When a passenger is assigned a specific seat
 * 
 * Event Naming Conventions:
 * - Use past tense (e.g., "Created", "Updated", "Cancelled")
 * - Be specific about what happened (e.g., "FlightCancelled" not "FlightChanged")
 * - Include the aggregate type (e.g., "BookingCreated" not just "Created")
 */
public abstract class DomainEvent implements ValueObject {
    
    /**
     * Unique identifier for this event.
     * Useful for deduplication and tracking.
     */
    private final UUID eventId;
    
    /**
     * Timestamp when this event occurred.
     * Should represent the business time, not the technical processing time.
     */
    private final Instant occurredOn;
    
    /**
     * Version of the event schema.
     * Useful for event evolution and backward compatibility.
     */
    private final int version;
    
    /**
     * Constructor for domain events.
     * Automatically generates an event ID and sets the occurrence time.
     */
    protected DomainEvent() {
        this(1); // Default to version 1
    }
    
    /**
     * Constructor for domain events with specific version.
     * 
     * @param version The version of this event schema
     */
    protected DomainEvent(int version) {
        this.eventId = UUID.randomUUID();
        this.occurredOn = Instant.now();
        this.version = version;
    }
    
    /**
     * Constructor for recreating events (e.g., from event store).
     * 
     * @param eventId The unique identifier of the event
     * @param occurredOn When the event occurred
     * @param version The version of the event schema
     */
    protected DomainEvent(UUID eventId, Instant occurredOn, int version) {
        this.eventId = Objects.requireNonNull(eventId, "Event ID cannot be null");
        this.occurredOn = Objects.requireNonNull(occurredOn, "Occurred on cannot be null");
        this.version = version;
    }
    
    /**
     * Gets the unique identifier of this event.
     * 
     * @return The event's unique ID
     */
    public UUID getEventId() {
        return eventId;
    }
    
    /**
     * Gets the timestamp when this event occurred.
     * 
     * @return The occurrence timestamp
     */
    public Instant getOccurredOn() {
        return occurredOn;
    }
    
    /**
     * Gets the version of this event schema.
     * 
     * @return The event version
     */
    public int getVersion() {
        return version;
    }
    
    /**
     * Gets the name of this event type.
     * By default, returns the simple class name.
     * Subclasses can override for custom naming.
     * 
     * @return The event type name
     */
    public String getEventType() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * Two domain events are equal if they have the same event ID.
     * This follows the principle that events are unique by their identifier.
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        
        DomainEvent other = (DomainEvent) obj;
        return Objects.equals(this.eventId, other.eventId);
    }
    
    /**
     * Hash code is based on the event ID.
     */
    @Override
    public int hashCode() {
        return Objects.hash(eventId);
    }
    
    /**
     * String representation includes event type, ID, and occurrence time.
     */
    @Override
    public String toString() {
        return String.format("%s{eventId=%s, occurredOn=%s, version=%d}", 
                           getEventType(), eventId, occurredOn, version);
    }
}
