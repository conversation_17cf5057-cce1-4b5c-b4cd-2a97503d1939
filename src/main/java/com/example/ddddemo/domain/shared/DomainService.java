package com.example.ddddemo.domain.shared;

/**
 * Marker interface for Domain Services in our DDD implementation.
 * 
 * Domain Services are used when a significant business process or transformation
 * doesn't naturally fit within a single Entity or Value Object. They encapsulate
 * domain logic that involves multiple domain objects or external systems.
 * 
 * When to use Domain Services:
 * 1. The operation is a significant business process
 * 2. The operation involves multiple aggregates
 * 3. The operation doesn't naturally belong to any single entity
 * 4. The operation requires external dependencies (but keep them minimal)
 * 
 * Characteristics of Domain Services:
 * - Stateless: They don't hold state between operations
 * - Express domain concepts: They represent important business operations
 * - Coordinate between entities: They orchestrate complex business logic
 * - Domain-focused: They contain pure business logic, not technical concerns
 * 
 * Examples in our aviation domain:
 * 
 * 1. PriceCalculationService:
 *    - Calculates ticket prices based on multiple pricing rules
 *    - Involves Flight, PricingRule, and Customer aggregates
 *    - Implements complex pricing algorithms
 * 
 * 2. FlightAvailabilityService:
 *    - Checks seat availability across multiple flights
 *    - Considers overbooking policies and passenger preferences
 *    - Coordinates between Flight and Booking aggregates
 * 
 * 3. LoyaltyPointsCalculationService:
 *    - Calculates loyalty points based on flight distance, fare class, etc.
 *    - Involves Customer, Flight, and Booking aggregates
 *    - Implements loyalty program business rules
 * 
 * 4. RouteOptimizationService:
 *    - Finds optimal flight routes for multi-leg journeys
 *    - Considers connection times, airline preferences, etc.
 *    - Works with multiple Flight and Airport entities
 * 
 * Anti-patterns to avoid:
 * - Don't use Domain Services for simple CRUD operations
 * - Don't put infrastructure concerns in Domain Services
 * - Don't make Domain Services too coarse-grained
 * - Don't use them when the logic clearly belongs to an entity
 * 
 * Implementation Guidelines:
 * 1. Keep them focused on a single business concept
 * 2. Make them stateless and side-effect free when possible
 * 3. Use dependency injection for external dependencies
 * 4. Write comprehensive unit tests
 * 5. Document the business rules they implement
 * 
 * Example implementation:
 * 
 * @Service
 * public class PriceCalculationService implements DomainService {
 *     
 *     public Money calculatePrice(Flight flight, Customer customer, BookingRequest request) {
 *         Money basePrice = flight.getBasePrice();
 *         
 *         // Apply various pricing rules
 *         basePrice = applySeasonalPricing(basePrice, flight.getDepartureDate());
 *         basePrice = applyAdvanceBookingDiscount(basePrice, request.getBookingDate(), flight.getDepartureDate());
 *         basePrice = applyLoyaltyDiscount(basePrice, customer);
 *         basePrice = applyDemandPricing(basePrice, flight.getAvailableSeats());
 *         
 *         return basePrice;
 *     }
 *     
 *     private Money applySeasonalPricing(Money basePrice, LocalDateTime departureDate) {
 *         // Seasonal pricing logic
 *     }
 *     
 *     // Other pricing methods...
 * }
 */
public interface DomainService {
    // Marker interface - no methods required
    // Implementing classes should follow domain service principles
}
