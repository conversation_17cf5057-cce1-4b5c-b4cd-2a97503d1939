package com.example.ddddemo.domain.shared;

/**
 * Exception thrown when domain event handling fails.
 * 
 * This exception is used to indicate that a domain event handler
 * encountered an error while processing an event. It provides
 * context about the failure and can be used by the event
 * publishing infrastructure to implement retry logic, dead
 * letter queues, and other error handling strategies.
 * 
 * The exception includes information about:
 * - The event that failed to process
 * - The handler that encountered the error
 * - Whether the failure is retryable
 * - The underlying cause of the failure
 */
public class DomainEventHandlingException extends Exception {
    
    /**
     * The domain event that failed to process.
     */
    private final DomainEvent event;
    
    /**
     * The name of the handler that failed.
     */
    private final String handlerName;
    
    /**
     * Whether this failure is retryable.
     */
    private final boolean retryable;
    
    /**
     * Creates a new DomainEventHandlingException.
     * 
     * @param message The error message
     * @param event The event that failed to process
     * @param handlerName The name of the handler that failed
     * @param retryable Whether the failure is retryable
     */
    public DomainEventHandlingException(String message, DomainEvent event, 
                                       String handlerName, boolean retryable) {
        super(message);
        this.event = event;
        this.handlerName = handlerName;
        this.retryable = retryable;
    }
    
    /**
     * Creates a new DomainEventHandlingException with a cause.
     * 
     * @param message The error message
     * @param cause The underlying cause
     * @param event The event that failed to process
     * @param handlerName The name of the handler that failed
     * @param retryable Whether the failure is retryable
     */
    public DomainEventHandlingException(String message, Throwable cause, DomainEvent event,
                                       String handlerName, boolean retryable) {
        super(message, cause);
        this.event = event;
        this.handlerName = handlerName;
        this.retryable = retryable;
    }
    
    /**
     * Gets the domain event that failed to process.
     * 
     * @return The domain event
     */
    public DomainEvent getEvent() {
        return event;
    }
    
    /**
     * Gets the name of the handler that failed.
     * 
     * @return The handler name
     */
    public String getHandlerName() {
        return handlerName;
    }
    
    /**
     * Checks if this failure is retryable.
     * 
     * @return true if the operation can be retried
     */
    public boolean isRetryable() {
        return retryable;
    }
    
    /**
     * Gets the event type that failed to process.
     * 
     * @return The event type name
     */
    public String getEventType() {
        return event != null ? event.getEventType() : "Unknown";
    }
    
    /**
     * Gets the event ID that failed to process.
     * 
     * @return The event ID
     */
    public String getEventId() {
        return event != null ? event.getEventId().toString() : "Unknown";
    }
    
    @Override
    public String toString() {
        return String.format("DomainEventHandlingException{eventType=%s, eventId=%s, handler=%s, retryable=%s, message=%s}",
                           getEventType(), getEventId(), handlerName, retryable, getMessage());
    }
}
