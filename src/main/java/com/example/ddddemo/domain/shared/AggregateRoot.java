package com.example.ddddemo.domain.shared;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Base class for Aggregate Roots in our DDD implementation.
 * 
 * In Domain Driven Design, an Aggregate Root is the only member of an aggregate
 * that outside objects are allowed to hold references to. It's the entry point
 * to the aggregate and is responsible for maintaining the aggregate's invariants.
 * 
 * Key responsibilities of Aggregate Roots:
 * - Enforce business invariants across the entire aggregate
 * - Control access to entities within the aggregate
 * - Publish domain events when important business events occur
 * - Ensure consistency boundaries are maintained
 * 
 * Aggregate Design Principles:
 * 1. Keep aggregates small - only include what must be consistent together
 * 2. Reference other aggregates by ID only, not by object reference
 * 3. Use eventual consistency between aggregates
 * 4. One repository per aggregate root
 * 5. Transactions should not span multiple aggregates
 * 
 * Examples in our aviation domain:
 * - Flight: Controls aircraft assignment, schedule, and seat availability
 * - PricingRule: Manages pricing factors and calculation logic
 * - Booking: Manages passenger details, tickets, and payment information
 * 
 * @param <ID> The type of the aggregate root's identifier
 */
public abstract class AggregateRoot<ID> extends Entity<ID> {
    
    /**
     * List of domain events that have occurred within this aggregate.
     * These events will be published when the aggregate is saved.
     */
    private final List<DomainEvent> domainEvents = new ArrayList<>();
    
    /**
     * Protected constructor for aggregate roots.
     */
    protected AggregateRoot() {
        super();
    }
    
    /**
     * Constructor with ID for creating aggregate roots with known identifiers.
     * 
     * @param id The unique identifier for this aggregate root
     */
    protected AggregateRoot(ID id) {
        super(id);
    }
    
    /**
     * Adds a domain event to be published when this aggregate is saved.
     * 
     * Domain events represent something important that happened in the domain
     * that other parts of the system might be interested in. They enable
     * loose coupling between different parts of the system.
     * 
     * @param event The domain event to add
     */
    protected void addDomainEvent(DomainEvent event) {
        if (event != null) {
            domainEvents.add(event);
        }
    }
    
    /**
     * Gets all domain events that have occurred within this aggregate.
     * Returns an unmodifiable list to prevent external modification.
     * 
     * @return Unmodifiable list of domain events
     */
    public List<DomainEvent> getDomainEvents() {
        return Collections.unmodifiableList(domainEvents);
    }
    
    /**
     * Clears all domain events from this aggregate.
     * This is typically called after the events have been published.
     */
    public void clearDomainEvents() {
        domainEvents.clear();
    }
    
    /**
     * Checks if this aggregate has any unpublished domain events.
     * 
     * @return true if there are unpublished domain events, false otherwise
     */
    public boolean hasDomainEvents() {
        return !domainEvents.isEmpty();
    }
    
    /**
     * Template method for validating aggregate invariants.
     * Subclasses should override this method to implement their specific
     * business rules and invariants.
     * 
     * This method should be called:
     * - After any state change that might violate invariants
     * - Before persisting the aggregate
     * - In unit tests to verify business rules
     * 
     * @throws IllegalStateException if any invariant is violated
     */
    protected void validateInvariants() {
        // Default implementation does nothing
        // Subclasses should override to implement specific validation logic
    }
    
    /**
     * Ensures that all aggregate invariants are satisfied.
     * This is a public method that can be called by application services
     * to validate the aggregate before persisting it.
     * 
     * @throws IllegalStateException if any invariant is violated
     */
    public final void ensureInvariants() {
        validateInvariants();
    }
}
