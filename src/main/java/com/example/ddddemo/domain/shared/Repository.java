package com.example.ddddemo.domain.shared;

import java.util.List;
import java.util.Optional;

/**
 * Base interface for Repositories in our DDD implementation.
 * 
 * In Domain Driven Design, a Repository encapsulates the logic needed to access
 * data sources. It centralizes common data access functionality, providing better
 * maintainability and decoupling the infrastructure or technology used to access
 * databases from the domain model layer.
 * 
 * Key principles of Repository pattern in DDD:
 * 1. One repository per aggregate root
 * 2. Repository interfaces belong to the domain layer
 * 3. Repository implementations belong to the infrastructure layer
 * 4. Repositories work with domain objects, not data transfer objects
 * 5. Repositories should provide collection-like interface
 * 
 * Benefits:
 * - Decouples domain from data access technology
 * - Enables easy unit testing with mock repositories
 * - Provides a consistent interface for data access
 * - Supports different storage mechanisms
 * - Centralizes query logic
 * 
 * Repository vs DAO (Data Access Object):
 * - Repository: Domain-centric, works with aggregates, collection-like interface
 * - DAO: Data-centric, works with tables/documents, CRUD-focused
 * 
 * Examples in our aviation domain:
 * - FlightRepository: Manages Flight aggregates
 * - PricingRuleRepository: Manages PricingRule aggregates  
 * - BookingRepository: Manages Booking aggregates
 * - CustomerRepository: Manages Customer aggregates
 * 
 * @param <T> The aggregate root type
 * @param <ID> The type of the aggregate root's identifier
 */
public interface Repository<T extends AggregateRoot<ID>, ID> {
    
    /**
     * Saves an aggregate root to the repository.
     * This method handles both new aggregates (insert) and existing ones (update).
     * 
     * Implementation notes:
     * - Should publish domain events after successful save
     * - Should validate aggregate invariants before saving
     * - Should handle optimistic locking if supported
     * 
     * @param aggregate The aggregate root to save
     * @return The saved aggregate root (may have updated ID or version)
     */
    T save(T aggregate);
    
    /**
     * Saves multiple aggregate roots in a single operation.
     * This can be more efficient than multiple individual saves.
     * 
     * @param aggregates The aggregate roots to save
     * @return The saved aggregate roots
     */
    List<T> saveAll(Iterable<T> aggregates);
    
    /**
     * Finds an aggregate root by its identifier.
     * 
     * @param id The unique identifier of the aggregate root
     * @return Optional containing the aggregate root if found, empty otherwise
     */
    Optional<T> findById(ID id);
    
    /**
     * Checks if an aggregate root exists with the given identifier.
     * 
     * @param id The unique identifier to check
     * @return true if an aggregate root exists with the given ID, false otherwise
     */
    boolean existsById(ID id);
    
    /**
     * Finds all aggregate roots in the repository.
     * 
     * Note: Use with caution in production systems as this can return
     * large amounts of data. Consider using pagination or specific queries instead.
     * 
     * @return List of all aggregate roots
     */
    List<T> findAll();
    
    /**
     * Counts the total number of aggregate roots in the repository.
     * 
     * @return The total count of aggregate roots
     */
    long count();
    
    /**
     * Deletes an aggregate root from the repository.
     * 
     * Implementation notes:
     * - Should validate that deletion is allowed (business rules)
     * - Should handle cascading deletes if necessary
     * - Should publish domain events for the deletion
     * 
     * @param aggregate The aggregate root to delete
     */
    void delete(T aggregate);
    
    /**
     * Deletes an aggregate root by its identifier.
     * 
     * @param id The unique identifier of the aggregate root to delete
     */
    void deleteById(ID id);
    
    /**
     * Deletes all aggregate roots from the repository.
     * 
     * Note: Use with extreme caution. This is typically only used
     * in testing scenarios or data migration scripts.
     */
    void deleteAll();
    
    /**
     * Flushes any pending changes to the underlying storage.
     * This is useful when you need to ensure changes are persisted
     * before continuing with other operations.
     */
    void flush();
    
    /**
     * Saves an aggregate root and flushes changes immediately.
     * This is equivalent to calling save() followed by flush().
     * 
     * @param aggregate The aggregate root to save and flush
     * @return The saved aggregate root
     */
    default T saveAndFlush(T aggregate) {
        T saved = save(aggregate);
        flush();
        return saved;
    }
}
