package com.example.ddddemo.domain.customer;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.util.Objects;

/**
 * Domain Event published when a passenger is removed from a booking.
 */
public class PassengerRemovedEvent extends DomainEvent {
    
    private final String bookingReference;
    private final String passengerName;
    
    public PassengerRemovedEvent(String bookingReference, String passengerName) {
        super();
        this.bookingReference = Objects.requireNonNull(bookingReference, "Booking reference cannot be null");
        this.passengerName = Objects.requireNonNull(passengerName, "Passenger name cannot be null");
    }
    
    public String getBookingReference() {
        return bookingReference;
    }
    
    public String getPassengerName() {
        return passengerName;
    }
    
    @Override
    public String toString() {
        return String.format("PassengerRemovedEvent{bookingRef='%s', passenger='%s'}", 
                           bookingReference, passengerName);
    }
}
