package com.example.ddddemo.domain.customer;

import com.example.ddddemo.domain.shared.DomainEvent;
import com.example.ddddemo.domain.shared.Money;
import java.util.Objects;

/**
 * Domain Event published when a booking's price is updated.
 */
public class BookingPriceUpdatedEvent extends DomainEvent {
    
    private final String bookingReference;
    private final Money oldPrice;
    private final Money newPrice;
    
    public BookingPriceUpdatedEvent(String bookingReference, Money oldPrice, Money newPrice) {
        super();
        this.bookingReference = Objects.requireNonNull(bookingReference, "Booking reference cannot be null");
        this.oldPrice = Objects.requireNonNull(oldPrice, "Old price cannot be null");
        this.newPrice = Objects.requireNonNull(newPrice, "New price cannot be null");
    }
    
    public String getBookingReference() {
        return bookingReference;
    }
    
    public Money getOldPrice() {
        return oldPrice;
    }
    
    public Money getNewPrice() {
        return newPrice;
    }
    
    public Money getPriceDifference() {
        return newPrice.subtract(oldPrice);
    }
    
    @Override
    public String toString() {
        return String.format("BookingPriceUpdatedEvent{bookingRef='%s', oldPrice=%s, newPrice=%s}", 
                           bookingReference, oldPrice, newPrice);
    }
}
