package com.example.ddddemo.domain.customer;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.util.Objects;

/**
 * Domain Event published when a booking is updated.
 */
public class BookingUpdatedEvent extends DomainEvent {
    
    private final String bookingReference;
    private final String updateDescription;
    
    public BookingUpdatedEvent(String bookingReference, String updateDescription) {
        super();
        this.bookingReference = Objects.requireNonNull(bookingReference, "Booking reference cannot be null");
        this.updateDescription = Objects.requireNonNull(updateDescription, "Update description cannot be null");
    }
    
    public String getBookingReference() {
        return bookingReference;
    }
    
    public String getUpdateDescription() {
        return updateDescription;
    }
    
    @Override
    public String toString() {
        return String.format("BookingUpdatedEvent{bookingRef='%s', update='%s'}", 
                           bookingReference, updateDescription);
    }
}
