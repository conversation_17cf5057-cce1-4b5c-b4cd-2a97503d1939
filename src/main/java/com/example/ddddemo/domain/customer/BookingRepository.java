package com.example.ddddemo.domain.customer;

import com.example.ddddemo.domain.shared.Repository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Booking aggregate roots.
 * 
 * This interface defines the contract for persisting and retrieving Booking aggregates.
 * It follows DDD principles by providing domain-specific query methods that support
 * the business use cases around booking management.
 * 
 * Key business scenarios supported:
 * - Finding bookings for customer service
 * - Managing booking status and payments
 * - Supporting booking analytics and reporting
 * - Handling booking modifications and cancellations
 */
public interface BookingRepository extends Repository<Booking, String> {
    
    /**
     * Finds a booking by its booking reference.
     * This is the primary lookup method for customer service and booking management.
     * 
     * @param bookingReference The booking reference to search for
     * @return Optional containing the booking if found
     */
    Optional<Booking> findByBookingReference(String bookingReference);
    
    /**
     * Finds all bookings for a specific flight.
     * Useful for flight operations and passenger manifests.
     * 
     * @param flightId The flight ID to search for
     * @return List of bookings for the specified flight
     */
    List<Booking> findByFlightId(UUID flightId);
    
    /**
     * Finds bookings by their status.
     * Useful for operations monitoring and customer service.
     * 
     * @param status The booking status to search for
     * @return List of bookings with the specified status
     */
    List<Booking> findByStatus(Booking.BookingStatus status);
    
    /**
     * Finds bookings by payment status.
     * Useful for payment processing and financial reporting.
     * 
     * @param paymentStatus The payment status to search for
     * @return List of bookings with the specified payment status
     */
    List<Booking> findByPaymentStatus(Booking.PaymentStatus paymentStatus);
    
    /**
     * Finds bookings created within a specific time period.
     * Useful for reporting and analytics.
     * 
     * @param startDate Start of the time period (inclusive)
     * @param endDate End of the time period (inclusive)
     * @return List of bookings created in the specified period
     */
    List<Booking> findByBookingDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Finds bookings by primary contact email.
     * Useful for customer service and duplicate detection.
     * 
     * @param email The email address to search for
     * @return List of bookings with the specified primary contact email
     */
    List<Booking> findByPrimaryContactEmail(String email);
    
    /**
     * Finds bookings by primary contact phone number.
     * Useful for customer service and verification.
     * 
     * @param phoneNumber The phone number to search for
     * @return List of bookings with the specified primary contact phone
     */
    List<Booking> findByPrimaryContactPhone(String phoneNumber);
    
    /**
     * Finds bookings containing a passenger with the specified name.
     * Useful for passenger lookup and customer service.
     * 
     * @param firstName The passenger's first name
     * @param lastName The passenger's last name
     * @return List of bookings containing a passenger with the specified name
     */
    List<Booking> findByPassengerName(String firstName, String lastName);
    
    /**
     * Finds bookings with a specific number of passengers.
     * Useful for analytics and capacity planning.
     * 
     * @param passengerCount The number of passengers
     * @return List of bookings with the specified passenger count
     */
    List<Booking> findByPassengerCount(int passengerCount);
    
    /**
     * Finds bookings that contain infant passengers.
     * Useful for special service planning and operations.
     * 
     * @return List of bookings containing infant passengers
     */
    List<Booking> findBookingsWithInfants();
    
    /**
     * Finds bookings that contain child passengers.
     * Useful for special service planning and operations.
     * 
     * @return List of bookings containing child passengers
     */
    List<Booking> findBookingsWithChildren();
    
    /**
     * Finds bookings with special requests or notes.
     * Useful for special service coordination.
     * 
     * @return List of bookings that have special requests or notes
     */
    List<Booking> findBookingsWithSpecialRequests();
    
    /**
     * Finds bookings by payment reference.
     * Useful for payment reconciliation and customer service.
     * 
     * @param paymentReference The payment reference to search for
     * @return Optional containing the booking if found
     */
    Optional<Booking> findByPaymentReference(String paymentReference);
    
    /**
     * Finds pending bookings that are older than the specified hours.
     * Useful for automated cleanup and reminder processes.
     * 
     * @param hours The number of hours to look back
     * @return List of pending bookings older than the specified hours
     */
    List<Booking> findPendingBookingsOlderThan(int hours);
    
    /**
     * Finds confirmed bookings for flights departing within the specified hours.
     * Useful for check-in reminders and operational planning.
     * 
     * @param hours The number of hours to look ahead
     * @return List of confirmed bookings for flights departing soon
     */
    List<Booking> findConfirmedBookingsForFlightsDepartingWithin(int hours);
    
    /**
     * Counts bookings by status for reporting purposes.
     * 
     * @param status The booking status to count
     * @return Number of bookings with the specified status
     */
    long countByStatus(Booking.BookingStatus status);
    
    /**
     * Counts bookings by payment status for financial reporting.
     * 
     * @param paymentStatus The payment status to count
     * @return Number of bookings with the specified payment status
     */
    long countByPaymentStatus(Booking.PaymentStatus paymentStatus);
    
    /**
     * Finds the most recent bookings for reporting and monitoring.
     * 
     * @param limit The maximum number of bookings to return
     * @return List of most recent bookings ordered by booking date (descending)
     */
    List<Booking> findMostRecentBookings(int limit);
    
    /**
     * Finds bookings with the highest total prices for analytics.
     * 
     * @param limit The maximum number of bookings to return
     * @return List of bookings ordered by total price (descending)
     */
    List<Booking> findHighestValueBookings(int limit);
}
