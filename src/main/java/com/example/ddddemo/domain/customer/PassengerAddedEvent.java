package com.example.ddddemo.domain.customer;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.util.Objects;

/**
 * Domain Event published when a passenger is added to a booking.
 */
public class PassengerAddedEvent extends DomainEvent {
    
    private final String bookingReference;
    private final String passengerName;
    
    public PassengerAddedEvent(String bookingReference, String passengerName) {
        super();
        this.bookingReference = Objects.requireNonNull(bookingReference, "Booking reference cannot be null");
        this.passengerName = Objects.requireNonNull(passengerName, "Passenger name cannot be null");
    }
    
    public String getBookingReference() {
        return bookingReference;
    }
    
    public String getPassengerName() {
        return passengerName;
    }
    
    @Override
    public String toString() {
        return String.format("PassengerAddedEvent{bookingRef='%s', passenger='%s'}", 
                           bookingReference, passengerName);
    }
}
