package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.ValueObject;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * FlightNumber Value Object representing airline flight identifiers.
 * 
 * Flight numbers are unique identifiers assigned by airlines to their scheduled flights.
 * They typically consist of a 2-3 letter airline code followed by 1-4 digits.
 * 
 * Examples:
 * - AA1234 (American Airlines flight 1234)
 * - BA456 (British Airways flight 456)
 * - LH789 (Lufthansa flight 789)
 * - DL2345 (Delta Air Lines flight 2345)
 * 
 * Business rules enforced:
 * - Must follow the pattern: 2-3 letters followed by 1-4 digits
 * - Letters must be uppercase (airline codes are standardized)
 * - Cannot be null or empty
 * - Total length must be between 3 and 7 characters
 * 
 * This Value Object demonstrates:
 * - Encapsulation of business rules
 * - Immutability
 * - Self-validation
 * - Domain-specific behavior (airline code extraction)
 */
public class FlightNumber implements ValueObject {
    
    /**
     * Pattern for validating flight numbers.
     * 2-3 uppercase letters followed by 1-4 digits.
     */
    private static final Pattern FLIGHT_NUMBER_PATTERN = Pattern.compile("^[A-Z]{2,3}\\d{1,4}$");
    
    /**
     * The complete flight number (e.g., "AA1234").
     */
    private final String number;
    
    /**
     * Creates a new FlightNumber.
     * 
     * @param number The flight number string (e.g., "AA1234")
     * @throws IllegalArgumentException if the flight number is invalid
     */
    public FlightNumber(String number) {
        validateFlightNumber(number);
        this.number = number.toUpperCase().trim();
    }
    
    /**
     * Validates the flight number format.
     * 
     * @param number The flight number to validate
     * @throws IllegalArgumentException if the flight number is invalid
     */
    private void validateFlightNumber(String number) {
        if (number == null || number.trim().isEmpty()) {
            throw new IllegalArgumentException("Flight number cannot be null or empty");
        }
        
        String trimmedNumber = number.trim().toUpperCase();
        
        if (!FLIGHT_NUMBER_PATTERN.matcher(trimmedNumber).matches()) {
            throw new IllegalArgumentException(
                String.format("Invalid flight number format: '%s'. " +
                            "Must be 2-3 letters followed by 1-4 digits (e.g., AA1234)", number));
        }
    }
    
    /**
     * Gets the complete flight number.
     * 
     * @return The flight number string
     */
    public String getNumber() {
        return number;
    }
    
    /**
     * Extracts the airline code from the flight number.
     * This demonstrates domain logic within a Value Object.
     * 
     * @return The airline code (2-3 letters)
     */
    public String getAirlineCode() {
        // Extract letters from the beginning of the flight number
        StringBuilder airlineCode = new StringBuilder();
        for (char c : number.toCharArray()) {
            if (Character.isLetter(c)) {
                airlineCode.append(c);
            } else {
                break;
            }
        }
        return airlineCode.toString();
    }
    
    /**
     * Extracts the flight number digits.
     * 
     * @return The numeric part of the flight number
     */
    public String getFlightDigits() {
        // Extract digits from the end of the flight number
        StringBuilder digits = new StringBuilder();
        for (char c : number.toCharArray()) {
            if (Character.isDigit(c)) {
                digits.append(c);
            }
        }
        return digits.toString();
    }
    
    /**
     * Gets the flight number as an integer (digits only).
     * 
     * @return The numeric part as integer
     */
    public int getFlightNumberAsInt() {
        return Integer.parseInt(getFlightDigits());
    }
    
    /**
     * Checks if this is a codeshare flight.
     * Codeshare flights typically have flight numbers above 3000.
     * This is a simplified business rule for demonstration.
     * 
     * @return true if this appears to be a codeshare flight
     */
    public boolean isCodeshare() {
        return getFlightNumberAsInt() >= 3000;
    }
    
    /**
     * Checks if this is a domestic flight based on airline code.
     * This is a simplified example - in reality, you'd need route information.
     * 
     * @return true if the airline code suggests a domestic carrier
     */
    public boolean isDomesticCarrier() {
        String airlineCode = getAirlineCode();
        return switch (airlineCode) {
            case "AA", "DL", "UA", "WN", "AS", "B6", "NK", "F9" -> true; // US carriers
            case "AC", "WJ" -> true; // Canadian carriers
            case "BA", "VS" -> true; // UK carriers
            default -> false;
        };
    }
    
    /**
     * Factory methods for creating common flight numbers.
     * This demonstrates the Factory pattern for Value Objects.
     */
    public static FlightNumber americanAirlines(int flightNumber) {
        return new FlightNumber("AA" + flightNumber);
    }
    
    public static FlightNumber delta(int flightNumber) {
        return new FlightNumber("DL" + flightNumber);
    }
    
    public static FlightNumber united(int flightNumber) {
        return new FlightNumber("UA" + flightNumber);
    }
    
    public static FlightNumber britishAirways(int flightNumber) {
        return new FlightNumber("BA" + flightNumber);
    }
    
    public static FlightNumber lufthansa(int flightNumber) {
        return new FlightNumber("LH" + flightNumber);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        FlightNumber that = (FlightNumber) obj;
        return Objects.equals(number, that.number);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(number);
    }
    
    @Override
    public String toString() {
        return number;
    }
}
