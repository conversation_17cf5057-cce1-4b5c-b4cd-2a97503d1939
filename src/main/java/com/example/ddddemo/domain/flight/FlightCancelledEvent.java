package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.util.Objects;
import java.util.UUID;

/**
 * Domain Event published when a flight is cancelled.
 * 
 * This event represents the business fact that a scheduled flight
 * has been cancelled. This triggers various business processes:
 * 
 * - Customer Service: Notify affected passengers
 * - Rebooking: Find alternative flights for passengers
 * - Refunds: Process refunds for cancelled bookings
 * - Operations: Release aircraft and crew assignments
 * - Revenue Management: Adjust capacity and pricing
 */
public class FlightCancelledEvent extends DomainEvent {
    
    /**
     * The unique identifier of the cancelled flight.
     */
    private final UUID flightId;
    
    /**
     * The flight number of the cancelled flight.
     */
    private final FlightNumber flightNumber;
    
    /**
     * The reason for cancellation.
     */
    private final String reason;
    
    /**
     * Creates a new FlightCancelledEvent.
     * 
     * @param flightId The flight identifier
     * @param flightNumber The flight number
     * @param reason The cancellation reason
     */
    public FlightCancelledEvent(UUID flightId, FlightNumber flightNumber, String reason) {
        super();
        this.flightId = Objects.requireNonNull(flightId, "Flight ID cannot be null");
        this.flightNumber = Objects.requireNonNull(flightNumber, "Flight number cannot be null");
        this.reason = reason != null ? reason : "No reason provided";
    }
    
    public UUID getFlightId() {
        return flightId;
    }
    
    public FlightNumber getFlightNumber() {
        return flightNumber;
    }
    
    public String getReason() {
        return reason;
    }
    
    @Override
    public String toString() {
        return String.format("FlightCancelledEvent{flightId=%s, flightNumber=%s, reason='%s'}", 
                           flightId, flightNumber, reason);
    }
}
