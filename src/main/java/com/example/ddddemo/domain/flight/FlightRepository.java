package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.Repository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Flight aggregate roots.
 * 
 * This interface defines the contract for persisting and retrieving Flight aggregates.
 * It follows DDD principles:
 * 
 * 1. Repository interface belongs to the domain layer
 * 2. Implementation belongs to the infrastructure layer
 * 3. Provides collection-like interface for Flight aggregates
 * 4. Includes domain-specific query methods
 * 5. Works with domain objects, not data transfer objects
 * 
 * The repository pattern provides several benefits:
 * - Decouples domain from persistence technology
 * - Enables easy unit testing with mock implementations
 * - Centralizes query logic
 * - Provides consistent interface for data access
 * 
 * Query methods are designed around business use cases:
 * - Finding flights by route for booking systems
 * - Finding flights by time range for scheduling
 * - Finding flights by status for operations
 * - Finding flights by aircraft for maintenance planning
 */
public interface FlightRepository extends Repository<Flight, UUID> {
    
    /**
     * Finds a flight by its flight number and departure date.
     * Flight numbers are typically unique per day for an airline.
     * 
     * @param flightNumber The flight number to search for
     * @param departureDate The departure date (time component ignored)
     * @return Optional containing the flight if found
     */
    Optional<Flight> findByFlightNumberAndDepartureDate(FlightNumber flightNumber, LocalDateTime departureDate);
    
    /**
     * Finds all flights for a specific route.
     * Useful for booking systems to show available flights.
     * 
     * @param route The route to search for
     * @return List of flights for the specified route
     */
    List<Flight> findByRoute(Route route);
    
    /**
     * Finds flights departing within a specific time range.
     * Useful for scheduling and operations planning.
     * 
     * @param startTime The start of the time range (inclusive)
     * @param endTime The end of the time range (inclusive)
     * @return List of flights departing within the time range
     */
    List<Flight> findByDepartureTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * Finds flights by their current status.
     * Useful for operations monitoring and reporting.
     * 
     * @param status The flight status to search for
     * @return List of flights with the specified status
     */
    List<Flight> findByStatus(Flight.FlightStatus status);
    
    /**
     * Finds flights assigned to a specific aircraft.
     * Useful for maintenance planning and aircraft utilization tracking.
     * 
     * @param aircraftRegistration The aircraft registration number
     * @return List of flights assigned to the aircraft
     */
    List<Flight> findByAircraftRegistration(String aircraftRegistration);
    
    /**
     * Finds flights departing from a specific airport.
     * Useful for airport operations and ground services.
     * 
     * @param origin The origin airport code
     * @return List of flights departing from the airport
     */
    List<Flight> findByOrigin(AirportCode origin);
    
    /**
     * Finds flights arriving at a specific airport.
     * Useful for airport operations and ground services.
     * 
     * @param destination The destination airport code
     * @return List of flights arriving at the airport
     */
    List<Flight> findByDestination(AirportCode destination);
    
    /**
     * Finds flights with available seats in a specific class.
     * Useful for booking systems to show bookable flights.
     * 
     * @param route The route to search for
     * @param seatClass The seat class to check availability for
     * @param minimumSeats The minimum number of available seats required
     * @return List of flights with sufficient available seats
     */
    List<Flight> findAvailableFlights(Route route, Flight.SeatClass seatClass, int minimumSeats);
    
    /**
     * Finds flights departing within the next specified hours.
     * Useful for operational alerts and passenger notifications.
     * 
     * @param hours The number of hours from now
     * @return List of flights departing within the specified time
     */
    List<Flight> findDepartingWithinHours(int hours);
    
    /**
     * Finds flights that are delayed (actual departure after scheduled).
     * Useful for operations monitoring and customer service.
     * 
     * @return List of delayed flights
     */
    List<Flight> findDelayedFlights();
    
    /**
     * Finds flights by airline code (extracted from flight number).
     * Useful for airline-specific reporting and analysis.
     * 
     * @param airlineCode The airline code (e.g., "AA", "DL")
     * @return List of flights for the specified airline
     */
    List<Flight> findByAirlineCode(String airlineCode);
    
    /**
     * Counts flights by status for reporting purposes.
     * 
     * @param status The flight status to count
     * @return Number of flights with the specified status
     */
    long countByStatus(Flight.FlightStatus status);
    
    /**
     * Finds the most popular routes based on flight frequency.
     * Useful for network planning and revenue management.
     * 
     * @param limit The maximum number of routes to return
     * @return List of routes ordered by flight frequency (descending)
     */
    List<Route> findMostPopularRoutes(int limit);
}
