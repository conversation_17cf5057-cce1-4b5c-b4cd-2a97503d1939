package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Domain Event published when a flight arrives.
 * 
 * This event represents the business fact that a flight has arrived
 * at its destination airport. This triggers various business processes:
 * 
 * - Operations: Update flight tracking and gate assignments
 * - Ground Services: Prepare for passenger disembarkation
 * - Baggage Handling: Begin baggage unloading process
 * - Crew Management: Complete crew duty time tracking
 * - Analytics: Record arrival performance and delays
 * - Customer Service: Update passenger notifications
 */
public class FlightArrivedEvent extends DomainEvent {
    
    /**
     * The unique identifier of the arrived flight.
     */
    private final UUID flightId;
    
    /**
     * The flight number of the arrived flight.
     */
    private final FlightNumber flightNumber;
    
    /**
     * The actual arrival time.
     */
    private final LocalDateTime actualArrival;
    
    /**
     * Creates a new FlightArrivedEvent.
     * 
     * @param flightId The flight identifier
     * @param flightNumber The flight number
     * @param actualArrival The actual arrival time
     */
    public FlightArrivedEvent(UUID flightId, FlightNumber flightNumber, LocalDateTime actualArrival) {
        super();
        this.flightId = Objects.requireNonNull(flightId, "Flight ID cannot be null");
        this.flightNumber = Objects.requireNonNull(flightNumber, "Flight number cannot be null");
        this.actualArrival = Objects.requireNonNull(actualArrival, "Actual arrival cannot be null");
    }
    
    public UUID getFlightId() {
        return flightId;
    }
    
    public FlightNumber getFlightNumber() {
        return flightNumber;
    }
    
    public LocalDateTime getActualArrival() {
        return actualArrival;
    }
    
    @Override
    public String toString() {
        return String.format("FlightArrivedEvent{flightId=%s, flightNumber=%s, actualArrival=%s}", 
                           flightId, flightNumber, actualArrival);
    }
}
