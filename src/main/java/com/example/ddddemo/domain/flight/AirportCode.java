package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.ValueObject;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * AirportCode Value Object representing IATA airport codes in our aviation system.
 * 
 * This is a perfect example of a Value Object in DDD:
 * - Immutable: Once created, it cannot be changed
 * - No identity: Two airport codes are equal if they have the same code
 * - Self-validating: Ensures the airport code follows IATA standards
 * - Meaningful: Represents a real business concept
 * 
 * IATA airport codes are 3-letter codes that uniquely identify airports worldwide.
 * Examples: JFK (John F. Kennedy International), LAX (Los Angeles International),
 * LHR (London Heathrow), CDG (Charles <PERSON>)
 * 
 * Business rules enforced:
 * - Must be exactly 3 characters long
 * - Must contain only uppercase letters
 * - Cannot be null or empty
 * 
 * This demonstrates DDD principles:
 * - Domain concepts should be explicitly modeled
 * - Business rules should be enforced at the domain level
 * - Value objects should be immutable and self-validating
 */
public class AirportCode implements ValueObject {
    
    /**
     * Pattern for validating IATA airport codes.
     * Must be exactly 3 uppercase letters.
     */
    private static final Pattern IATA_CODE_PATTERN = Pattern.compile("^[A-Z]{3}$");
    
    /**
     * The 3-letter IATA airport code.
     */
    private final String code;
    
    /**
     * Creates a new AirportCode.
     * 
     * @param code The 3-letter IATA airport code (e.g., "JFK", "LAX")
     * @throws IllegalArgumentException if the code is invalid
     */
    public AirportCode(String code) {
        validateCode(code);
        this.code = code.toUpperCase(); // Ensure uppercase
    }
    
    /**
     * Validates the airport code according to IATA standards.
     * 
     * @param code The code to validate
     * @throws IllegalArgumentException if the code is invalid
     */
    private void validateCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("Airport code cannot be null or empty");
        }
        
        String trimmedCode = code.trim().toUpperCase();
        
        if (!IATA_CODE_PATTERN.matcher(trimmedCode).matches()) {
            throw new IllegalArgumentException(
                String.format("Invalid airport code: '%s'. Must be exactly 3 uppercase letters.", code));
        }
    }
    
    /**
     * Gets the airport code.
     * 
     * @return The 3-letter IATA airport code
     */
    public String getCode() {
        return code;
    }
    
    /**
     * Factory method for creating common airport codes.
     * This demonstrates the Factory pattern for Value Objects.
     */
    public static AirportCode jfk() {
        return new AirportCode("JFK");
    }
    
    public static AirportCode lax() {
        return new AirportCode("LAX");
    }
    
    public static AirportCode lhr() {
        return new AirportCode("LHR");
    }
    
    public static AirportCode cdg() {
        return new AirportCode("CDG");
    }
    
    public static AirportCode dfw() {
        return new AirportCode("DFW");
    }
    
    public static AirportCode ord() {
        return new AirportCode("ORD");
    }
    
    /**
     * Checks if this airport code represents a major hub airport.
     * This is an example of domain logic in a Value Object.
     * 
     * @return true if this is a major hub airport
     */
    public boolean isMajorHub() {
        return switch (code) {
            case "JFK", "LAX", "LHR", "CDG", "DFW", "ORD", "ATL", "DEN", "PHX", "IAH" -> true;
            default -> false;
        };
    }
    
    /**
     * Gets the time zone identifier for this airport.
     * This demonstrates how Value Objects can contain business logic.
     * 
     * Note: In a real system, this would likely be retrieved from a database
     * or external service. This is simplified for demonstration purposes.
     * 
     * @return The time zone identifier
     */
    public String getTimeZone() {
        return switch (code) {
            case "JFK", "LGA", "EWR" -> "America/New_York";
            case "LAX", "SFO", "SAN" -> "America/Los_Angeles";
            case "ORD", "MDW" -> "America/Chicago";
            case "DEN" -> "America/Denver";
            case "LHR", "LGW" -> "Europe/London";
            case "CDG", "ORY" -> "Europe/Paris";
            case "DFW", "IAH" -> "America/Chicago";
            case "ATL" -> "America/New_York";
            case "PHX" -> "America/Phoenix";
            default -> "UTC"; // Default fallback
        };
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        AirportCode that = (AirportCode) obj;
        return Objects.equals(code, that.code);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(code);
    }
    
    @Override
    public String toString() {
        return code;
    }
}
