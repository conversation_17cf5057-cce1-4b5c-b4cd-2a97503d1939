package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.util.Objects;
import java.util.UUID;

/**
 * Domain Event published when seats are reserved on a flight.
 * 
 * This event represents the business fact that seats have been reserved
 * for a booking. This is important for:
 * 
 * - Revenue Management: To adjust pricing based on seat availability
 * - Overbooking Management: To track reservation levels
 * - Customer Service: To handle seat assignment requests
 * - Analytics: To track booking patterns and demand
 */
public class SeatsReservedEvent extends DomainEvent {
    
    /**
     * The unique identifier of the flight.
     */
    private final UUID flightId;
    
    /**
     * The seat class for the reserved seats.
     */
    private final Flight.SeatClass seatClass;
    
    /**
     * The number of seats reserved.
     */
    private final int numberOfSeats;
    
    /**
     * Creates a new SeatsReservedEvent.
     * 
     * @param flightId The flight identifier
     * @param seatClass The seat class
     * @param numberOfSeats The number of seats reserved
     */
    public SeatsReservedEvent(UUID flightId, Flight.SeatClass seatClass, int numberOfSeats) {
        super();
        this.flightId = Objects.requireNonNull(flightId, "Flight ID cannot be null");
        this.seatClass = Objects.requireNonNull(seatClass, "Seat class cannot be null");
        this.numberOfSeats = numberOfSeats;
        
        if (numberOfSeats <= 0) {
            throw new IllegalArgumentException("Number of seats must be positive");
        }
    }
    
    public UUID getFlightId() {
        return flightId;
    }
    
    public Flight.SeatClass getSeatClass() {
        return seatClass;
    }
    
    public int getNumberOfSeats() {
        return numberOfSeats;
    }
    
    @Override
    public String toString() {
        return String.format("SeatsReservedEvent{flightId=%s, seatClass=%s, numberOfSeats=%d}", 
                           flightId, seatClass, numberOfSeats);
    }
}
