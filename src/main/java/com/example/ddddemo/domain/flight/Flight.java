package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.AggregateRoot;
import com.example.ddddemo.domain.shared.Money;
import java.time.LocalDateTime;
import java.time.Duration;
import java.util.Objects;
import java.util.UUID;

/**
 * Flight Aggregate Root representing scheduled flights in our aviation system.
 * 
 * Flight is an Aggregate Root because:
 * - It has a clear identity and lifecycle
 * - It controls access to related entities (Aircraft)
 * - It maintains important business invariants (scheduling, capacity)
 * - It publishes domain events (FlightScheduled, FlightCancelled)
 * - External systems interact with flights, not individual aircraft
 * 
 * The Flight aggregate boundary includes:
 * - Flight (Aggregate Root)
 * - Aircraft (Entity within the aggregate)
 * - Route (Value Object)
 * - FlightNumber (Value Object)
 * 
 * Key business invariants maintained:
 * - Flight must have valid departure and arrival times
 * - Aircraft must be available and suitable for the route
 * - Seat availability must be tracked accurately
 * - Flight status transitions must follow business rules
 * 
 * This demonstrates core DDD concepts:
 * - Aggregate design with proper boundaries
 * - Business invariant enforcement
 * - Domain event publishing
 * - Rich domain behavior
 */
public class Flight extends AggregateRoot<UUID> {
    
    /**
     * Unique flight identifier.
     */
    private final UUID flightId;
    
    /**
     * Flight number (e.g., "AA1234").
     */
    private final FlightNumber flightNumber;
    
    /**
     * Flight route (origin to destination).
     */
    private final Route route;
    
    /**
     * Assigned aircraft for this flight.
     */
    private Aircraft aircraft;
    
    /**
     * Scheduled departure time.
     */
    private LocalDateTime scheduledDeparture;
    
    /**
     * Scheduled arrival time.
     */
    private LocalDateTime scheduledArrival;
    
    /**
     * Actual departure time (null if not departed).
     */
    private LocalDateTime actualDeparture;
    
    /**
     * Actual arrival time (null if not arrived).
     */
    private LocalDateTime actualArrival;
    
    /**
     * Base price for economy class tickets.
     */
    private Money basePrice;
    
    /**
     * Current flight status.
     */
    private FlightStatus status;
    
    /**
     * Number of available economy seats.
     */
    private int availableEconomySeats;
    
    /**
     * Number of available business seats.
     */
    private int availableBusinessSeats;
    
    /**
     * Number of available first class seats.
     */
    private int availableFirstClassSeats;
    
    /**
     * Creates a new scheduled flight.
     * 
     * @param flightNumber The flight number
     * @param route The flight route
     * @param aircraft The assigned aircraft
     * @param scheduledDeparture Scheduled departure time
     * @param scheduledArrival Scheduled arrival time
     * @param basePrice Base price for economy tickets
     */
    public Flight(FlightNumber flightNumber, Route route, Aircraft aircraft,
                 LocalDateTime scheduledDeparture, LocalDateTime scheduledArrival,
                 Money basePrice) {
        super();
        
        this.flightId = UUID.randomUUID();
        this.flightNumber = Objects.requireNonNull(flightNumber, "Flight number cannot be null");
        this.route = Objects.requireNonNull(route, "Route cannot be null");
        this.basePrice = Objects.requireNonNull(basePrice, "Base price cannot be null");
        
        assignAircraft(aircraft);
        setSchedule(scheduledDeparture, scheduledArrival);
        
        this.status = FlightStatus.SCHEDULED;
        initializeAvailableSeats();
        
        // Publish domain event
        addDomainEvent(new FlightScheduledEvent(flightId, flightNumber.getNumber(),
                      route.getRouteCode(), scheduledDeparture));
    }
    
    /**
     * Assigns an aircraft to this flight.
     * This enforces business rules about aircraft availability and suitability.
     */
    private void assignAircraft(Aircraft aircraft) {
        Objects.requireNonNull(aircraft, "Aircraft cannot be null");
        
        if (!aircraft.isAvailableForFlight()) {
            throw new IllegalArgumentException(
                String.format("Aircraft %s is not available for flight assignment", 
                            aircraft.getRegistration()));
        }
        
        this.aircraft = aircraft;
    }
    
    /**
     * Sets the flight schedule and validates timing constraints.
     */
    private void setSchedule(LocalDateTime departure, LocalDateTime arrival) {
        Objects.requireNonNull(departure, "Scheduled departure cannot be null");
        Objects.requireNonNull(arrival, "Scheduled arrival cannot be null");
        
        if (!arrival.isAfter(departure)) {
            throw new IllegalArgumentException("Arrival time must be after departure time");
        }
        
        // Business rule: Minimum flight duration (e.g., 30 minutes for taxi time)
        Duration flightDuration = Duration.between(departure, arrival);
        if (flightDuration.toMinutes() < 30) {
            throw new IllegalArgumentException("Flight duration must be at least 30 minutes");
        }
        
        this.scheduledDeparture = departure;
        this.scheduledArrival = arrival;
    }
    
    /**
     * Initializes available seats based on aircraft configuration.
     */
    private void initializeAvailableSeats() {
        this.availableEconomySeats = aircraft.getEconomyClassSeats();
        this.availableBusinessSeats = aircraft.getBusinessClassSeats();
        this.availableFirstClassSeats = aircraft.getFirstClassSeats();
    }
    
    // Getters
    public UUID getFlightId() {
        return flightId;
    }
    
    public FlightNumber getFlightNumber() {
        return flightNumber;
    }
    
    public Route getRoute() {
        return route;
    }
    
    public Aircraft getAircraft() {
        return aircraft;
    }
    
    public LocalDateTime getScheduledDeparture() {
        return scheduledDeparture;
    }
    
    public LocalDateTime getScheduledArrival() {
        return scheduledArrival;
    }
    
    public LocalDateTime getActualDeparture() {
        return actualDeparture;
    }
    
    public LocalDateTime getActualArrival() {
        return actualArrival;
    }
    
    public Money getBasePrice() {
        return basePrice;
    }
    
    public FlightStatus getStatus() {
        return status;
    }
    
    public int getAvailableEconomySeats() {
        return availableEconomySeats;
    }
    
    public int getAvailableBusinessSeats() {
        return availableBusinessSeats;
    }
    
    public int getAvailableFirstClassSeats() {
        return availableFirstClassSeats;
    }
    
    /**
     * Gets total available seats across all classes.
     */
    public int getTotalAvailableSeats() {
        return availableEconomySeats + availableBusinessSeats + availableFirstClassSeats;
    }
    
    /**
     * Checks if the flight has available seats in the specified class.
     */
    public boolean hasAvailableSeats(SeatClass seatClass, int requestedSeats) {
        return switch (seatClass) {
            case ECONOMY -> availableEconomySeats >= requestedSeats;
            case BUSINESS -> availableBusinessSeats >= requestedSeats;
            case FIRST -> availableFirstClassSeats >= requestedSeats;
        };
    }
    
    /**
     * Reserves seats for a booking.
     * This is a key business operation that maintains seat availability invariants.
     */
    public void reserveSeats(SeatClass seatClass, int numberOfSeats) {
        if (numberOfSeats <= 0) {
            throw new IllegalArgumentException("Number of seats must be positive");
        }
        
        if (!hasAvailableSeats(seatClass, numberOfSeats)) {
            throw new IllegalStateException(
                String.format("Not enough available %s seats. Requested: %d, Available: %d",
                            seatClass, numberOfSeats, getAvailableSeats(seatClass)));
        }
        
        switch (seatClass) {
            case ECONOMY -> availableEconomySeats -= numberOfSeats;
            case BUSINESS -> availableBusinessSeats -= numberOfSeats;
            case FIRST -> availableFirstClassSeats -= numberOfSeats;
        }
        
        // Publish domain event for seat reservation
        addDomainEvent(new SeatsReservedEvent(flightId, seatClass, numberOfSeats));
    }
    
    private int getAvailableSeats(SeatClass seatClass) {
        return switch (seatClass) {
            case ECONOMY -> availableEconomySeats;
            case BUSINESS -> availableBusinessSeats;
            case FIRST -> availableFirstClassSeats;
        };
    }
    
    /**
     * Cancels the flight.
     * This demonstrates state transition with business rules.
     */
    public void cancel(String reason) {
        if (status == FlightStatus.CANCELLED) {
            throw new IllegalStateException("Flight is already cancelled");
        }
        
        if (status == FlightStatus.DEPARTED || status == FlightStatus.ARRIVED) {
            throw new IllegalStateException("Cannot cancel a flight that has already departed");
        }
        
        this.status = FlightStatus.CANCELLED;
        addDomainEvent(new FlightCancelledEvent(flightId, flightNumber, reason));
    }
    
    /**
     * Marks the flight as departed.
     */
    public void depart() {
        if (status != FlightStatus.SCHEDULED) {
            throw new IllegalStateException("Only scheduled flights can depart");
        }
        
        this.status = FlightStatus.DEPARTED;
        this.actualDeparture = LocalDateTime.now();
        addDomainEvent(new FlightDepartedEvent(flightId, flightNumber, actualDeparture));
    }
    
    /**
     * Marks the flight as arrived.
     */
    public void arrive() {
        if (status != FlightStatus.DEPARTED) {
            throw new IllegalStateException("Flight must be departed before it can arrive");
        }
        
        this.status = FlightStatus.ARRIVED;
        this.actualArrival = LocalDateTime.now();
        addDomainEvent(new FlightArrivedEvent(flightId, flightNumber, actualArrival));
    }
    
    /**
     * Calculates the load factor (percentage of seats occupied).
     */
    public double getLoadFactor() {
        int totalSeats = aircraft.getTotalSeats();
        int occupiedSeats = totalSeats - getTotalAvailableSeats();
        return totalSeats > 0 ? (double) occupiedSeats / totalSeats * 100 : 0;
    }
    
    /**
     * Checks if this is a domestic flight.
     */
    public boolean isDomestic() {
        return route.isDomestic();
    }
    
    /**
     * Gets the scheduled flight duration.
     */
    public Duration getScheduledDuration() {
        return Duration.between(scheduledDeparture, scheduledArrival);
    }
    
    @Override
    protected void validateInvariants() {
        // Validate that seat counts don't exceed aircraft capacity
        int totalReserved = (aircraft.getEconomyClassSeats() - availableEconomySeats) +
                           (aircraft.getBusinessClassSeats() - availableBusinessSeats) +
                           (aircraft.getFirstClassSeats() - availableFirstClassSeats);
        
        if (totalReserved > aircraft.getTotalSeats()) {
            throw new IllegalStateException("Reserved seats exceed aircraft capacity");
        }
        
        // Validate schedule consistency
        if (actualDeparture != null && actualArrival != null) {
            if (!actualArrival.isAfter(actualDeparture)) {
                throw new IllegalStateException("Actual arrival must be after actual departure");
            }
        }
    }
    
    @Override
    public String toString() {
        return String.format("Flight{%s, %s, %s, status=%s}", 
                           flightNumber, route, scheduledDeparture, status);
    }
    
    /**
     * Enumeration for flight status.
     */
    public enum FlightStatus {
        SCHEDULED, DEPARTED, ARRIVED, CANCELLED, DELAYED
    }
    
    /**
     * Enumeration for seat classes.
     */
    public enum SeatClass {
        ECONOMY, BUSINESS, FIRST
    }
}
