package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.Entity;
import java.util.Objects;

/**
 * Aircraft Entity representing physical aircraft in our aviation system.
 * 
 * Aircraft is an Entity because:
 * - It has a unique identity (registration number)
 * - Two aircraft are the same if they have the same registration
 * - It can change attributes (maintenance status, location) while maintaining identity
 * - It has a lifecycle (manufactured, in-service, retired)
 * 
 * In our domain model, Aircraft is NOT an aggregate root because:
 * - It's managed as part of the Flight aggregate
 * - Flight controls aircraft assignment and scheduling
 * - Aircraft doesn't have its own repository
 * - Business operations typically involve flights, not individual aircraft
 * 
 * This demonstrates the DDD principle that not all entities are aggregate roots.
 * The aggregate boundary is determined by consistency requirements and
 * transactional boundaries, not just by entity relationships.
 */
public class Aircraft extends Entity<String> {
    
    /**
     * Aircraft registration number (tail number).
     * This serves as the unique identifier for the aircraft.
     * Examples: "N12345", "G-ABCD", "D-EFGH"
     */
    private final String registration;
    
    /**
     * Aircraft model/type (e.g., "Boeing 737-800", "Airbus A320").
     */
    private final String model;
    
    /**
     * Total seating capacity of the aircraft.
     */
    private final int totalSeats;
    
    /**
     * Number of first class seats.
     */
    private final int firstClassSeats;
    
    /**
     * Number of business class seats.
     */
    private final int businessClassSeats;
    
    /**
     * Number of economy class seats.
     */
    private final int economyClassSeats;
    
    /**
     * Current operational status of the aircraft.
     */
    private AircraftStatus status;
    
    /**
     * Current location of the aircraft (airport code).
     */
    private AirportCode currentLocation;
    
    /**
     * Creates a new Aircraft.
     * 
     * @param registration The aircraft registration number
     * @param model The aircraft model
     * @param firstClassSeats Number of first class seats
     * @param businessClassSeats Number of business class seats
     * @param economyClassSeats Number of economy class seats
     */
    public Aircraft(String registration, String model, 
                   int firstClassSeats, int businessClassSeats, int economyClassSeats) {
        super(registration);
        validateRegistration(registration);
        validateModel(model);
        validateSeatConfiguration(firstClassSeats, businessClassSeats, economyClassSeats);
        
        this.registration = registration.toUpperCase().trim();
        this.model = model.trim();
        this.firstClassSeats = firstClassSeats;
        this.businessClassSeats = businessClassSeats;
        this.economyClassSeats = economyClassSeats;
        this.totalSeats = firstClassSeats + businessClassSeats + economyClassSeats;
        this.status = AircraftStatus.IN_SERVICE;
    }
    
    /**
     * Convenience constructor for single-class aircraft.
     */
    public Aircraft(String registration, String model, int totalSeats) {
        this(registration, model, 0, 0, totalSeats);
    }
    
    private void validateRegistration(String registration) {
        if (registration == null || registration.trim().isEmpty()) {
            throw new IllegalArgumentException("Aircraft registration cannot be null or empty");
        }
        // In a real system, you might validate against specific registration patterns
        // for different countries (e.g., N-prefix for US, G-prefix for UK)
    }
    
    private void validateModel(String model) {
        if (model == null || model.trim().isEmpty()) {
            throw new IllegalArgumentException("Aircraft model cannot be null or empty");
        }
    }
    
    private void validateSeatConfiguration(int firstClass, int business, int economy) {
        if (firstClass < 0 || business < 0 || economy < 0) {
            throw new IllegalArgumentException("Seat counts cannot be negative");
        }
        if (firstClass + business + economy == 0) {
            throw new IllegalArgumentException("Aircraft must have at least one seat");
        }
    }
    
    // Getters
    public String getRegistration() {
        return registration;
    }
    
    public String getModel() {
        return model;
    }
    
    public int getTotalSeats() {
        return totalSeats;
    }
    
    public int getFirstClassSeats() {
        return firstClassSeats;
    }
    
    public int getBusinessClassSeats() {
        return businessClassSeats;
    }
    
    public int getEconomyClassSeats() {
        return economyClassSeats;
    }
    
    public AircraftStatus getStatus() {
        return status;
    }
    
    public AirportCode getCurrentLocation() {
        return currentLocation;
    }
    
    /**
     * Updates the aircraft's operational status.
     * This is a domain operation that might trigger business rules.
     * 
     * @param newStatus The new operational status
     */
    public void updateStatus(AircraftStatus newStatus) {
        if (newStatus == null) {
            throw new IllegalArgumentException("Aircraft status cannot be null");
        }
        
        // Business rule: Aircraft in maintenance cannot be assigned to flights
        if (this.status == AircraftStatus.IN_MAINTENANCE && newStatus == AircraftStatus.IN_SERVICE) {
            // In a real system, you might check maintenance completion certificates
        }
        
        this.status = newStatus;
    }
    
    /**
     * Updates the aircraft's current location.
     * 
     * @param location The new location (airport code)
     */
    public void updateLocation(AirportCode location) {
        this.currentLocation = location;
    }
    
    /**
     * Checks if the aircraft is available for flight assignment.
     * 
     * @return true if the aircraft can be assigned to flights
     */
    public boolean isAvailableForFlight() {
        return status == AircraftStatus.IN_SERVICE;
    }
    
    /**
     * Checks if this is a wide-body aircraft.
     * This is a simplified business rule based on total seats.
     * 
     * @return true if this is considered a wide-body aircraft
     */
    public boolean isWideBody() {
        return totalSeats >= 200;
    }
    
    /**
     * Checks if this aircraft has premium cabin classes.
     * 
     * @return true if the aircraft has first or business class seats
     */
    public boolean hasPremiumCabins() {
        return firstClassSeats > 0 || businessClassSeats > 0;
    }
    
    /**
     * Gets the aircraft manufacturer based on the model.
     * This demonstrates domain logic within an entity.
     * 
     * @return The manufacturer name
     */
    public String getManufacturer() {
        String lowerModel = model.toLowerCase();
        if (lowerModel.contains("boeing") || lowerModel.contains("737") || 
            lowerModel.contains("747") || lowerModel.contains("777") || lowerModel.contains("787")) {
            return "Boeing";
        } else if (lowerModel.contains("airbus") || lowerModel.contains("a320") || 
                   lowerModel.contains("a330") || lowerModel.contains("a350") || lowerModel.contains("a380")) {
            return "Airbus";
        } else if (lowerModel.contains("embraer") || lowerModel.contains("e-jet")) {
            return "Embraer";
        } else if (lowerModel.contains("bombardier") || lowerModel.contains("crj")) {
            return "Bombardier";
        }
        return "Unknown";
    }
    
    @Override
    public String toString() {
        return String.format("Aircraft{registration='%s', model='%s', seats=%d, status=%s}", 
                           registration, model, totalSeats, status);
    }
    
    /**
     * Enumeration for aircraft operational status.
     */
    public enum AircraftStatus {
        IN_SERVICE("In Service"),
        IN_MAINTENANCE("In Maintenance"),
        OUT_OF_SERVICE("Out of Service"),
        RETIRED("Retired");
        
        private final String displayName;
        
        AircraftStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
