package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Domain Event published when a flight departs.
 * 
 * This event represents the business fact that a flight has departed
 * from its origin airport. This triggers various business processes:
 * 
 * - Operations: Update flight tracking systems
 * - Customer Service: Notify passengers of departure
 * - Ground Services: Release gate and ground equipment
 * - Crew Management: Update crew duty time tracking
 * - Analytics: Record on-time performance data
 */
public class FlightDepartedEvent extends DomainEvent {
    
    /**
     * The unique identifier of the departed flight.
     */
    private final UUID flightId;
    
    /**
     * The flight number of the departed flight.
     */
    private final FlightNumber flightNumber;
    
    /**
     * The actual departure time.
     */
    private final LocalDateTime actualDeparture;
    
    /**
     * Creates a new FlightDepartedEvent.
     * 
     * @param flightId The flight identifier
     * @param flightNumber The flight number
     * @param actualDeparture The actual departure time
     */
    public FlightDepartedEvent(UUID flightId, FlightNumber flightNumber, LocalDateTime actualDeparture) {
        super();
        this.flightId = Objects.requireNonNull(flightId, "Flight ID cannot be null");
        this.flightNumber = Objects.requireNonNull(flightNumber, "Flight number cannot be null");
        this.actualDeparture = Objects.requireNonNull(actualDeparture, "Actual departure cannot be null");
    }
    
    public UUID getFlightId() {
        return flightId;
    }
    
    public FlightNumber getFlightNumber() {
        return flightNumber;
    }
    
    public LocalDateTime getActualDeparture() {
        return actualDeparture;
    }
    
    @Override
    public String toString() {
        return String.format("FlightDepartedEvent{flightId=%s, flightNumber=%s, actualDeparture=%s}", 
                           flightId, flightNumber, actualDeparture);
    }
}
