package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.ValueObject;
import java.util.Objects;

/**
 * Route Value Object representing a flight path between two airports.
 * 
 * A route defines the origin and destination airports for a flight.
 * It's a fundamental concept in aviation that represents the path
 * an aircraft takes from one airport to another.
 * 
 * Key characteristics:
 * - Immutable: Once created, origin and destination cannot change
 * - Self-validating: Ensures origin and destination are different
 * - Rich behavior: Provides methods for route analysis
 * - Domain-focused: Contains aviation-specific business logic
 * 
 * Business rules enforced:
 * - Origin and destination must be different airports
 * - Both origin and destination must be valid airport codes
 * - Cannot have null origin or destination
 * 
 * This demonstrates several DDD concepts:
 * - Value Objects can contain other Value Objects (composition)
 * - Business rules should be enforced in the domain model
 * - Domain logic belongs in domain objects, not in services
 */
public class Route implements ValueObject {
    
    /**
     * The origin airport code.
     */
    private final AirportCode origin;
    
    /**
     * The destination airport code.
     */
    private final AirportCode destination;
    
    /**
     * Creates a new Route.
     * 
     * @param origin The origin airport code
     * @param destination The destination airport code
     * @throws IllegalArgumentException if origin equals destination or either is null
     */
    public Route(AirportCode origin, AirportCode destination) {
        validateRoute(origin, destination);
        this.origin = origin;
        this.destination = destination;
    }
    
    /**
     * Convenience constructor using string airport codes.
     * 
     * @param origin The origin airport code as string
     * @param destination The destination airport code as string
     */
    public Route(String origin, String destination) {
        this(new AirportCode(origin), new AirportCode(destination));
    }
    
    /**
     * Validates the route business rules.
     * 
     * @param origin The origin airport
     * @param destination The destination airport
     * @throws IllegalArgumentException if validation fails
     */
    private void validateRoute(AirportCode origin, AirportCode destination) {
        if (origin == null) {
            throw new IllegalArgumentException("Origin airport cannot be null");
        }
        if (destination == null) {
            throw new IllegalArgumentException("Destination airport cannot be null");
        }
        if (origin.equals(destination)) {
            throw new IllegalArgumentException(
                String.format("Origin and destination cannot be the same: %s", origin));
        }
    }
    
    /**
     * Gets the origin airport code.
     * 
     * @return The origin airport
     */
    public AirportCode getOrigin() {
        return origin;
    }
    
    /**
     * Gets the destination airport code.
     * 
     * @return The destination airport
     */
    public AirportCode getDestination() {
        return destination;
    }
    
    /**
     * Creates the reverse route (destination to origin).
     * This is useful for return flights.
     * 
     * @return A new Route with origin and destination swapped
     */
    public Route reverse() {
        return new Route(destination, origin);
    }
    
    /**
     * Checks if this is a domestic route.
     * This is a simplified implementation - in reality, you'd need
     * a mapping of airports to countries.
     * 
     * @return true if both airports appear to be in the same country
     */
    public boolean isDomestic() {
        // Simplified logic based on common US airport codes
        boolean originIsUS = isUSAirport(origin);
        boolean destinationIsUS = isUSAirport(destination);
        
        // Both US or both non-US (simplified)
        return originIsUS == destinationIsUS;
    }
    
    /**
     * Checks if this is an international route.
     * 
     * @return true if this appears to be an international route
     */
    public boolean isInternational() {
        return !isDomestic();
    }
    
    /**
     * Checks if this route involves a major hub airport.
     * 
     * @return true if either origin or destination is a major hub
     */
    public boolean involvesHub() {
        return origin.isMajorHub() || destination.isMajorHub();
    }
    
    /**
     * Checks if both airports are major hubs.
     * Hub-to-hub routes are typically high-traffic routes.
     * 
     * @return true if both origin and destination are major hubs
     */
    public boolean isHubToHub() {
        return origin.isMajorHub() && destination.isMajorHub();
    }
    
    /**
     * Gets a string representation suitable for display.
     * 
     * @return Route in "ORIGIN-DESTINATION" format
     */
    public String getRouteCode() {
        return origin.getCode() + "-" + destination.getCode();
    }
    
    /**
     * Checks if this route matches the given route code.
     * 
     * @param routeCode Route code in "ORIGIN-DESTINATION" format
     * @return true if the route code matches this route
     */
    public boolean matchesRouteCode(String routeCode) {
        return getRouteCode().equals(routeCode);
    }
    
    /**
     * Simplified method to check if an airport is in the US.
     * In a real system, this would use a proper airport database.
     */
    private boolean isUSAirport(AirportCode airport) {
        String code = airport.getCode();
        return switch (code) {
            case "JFK", "LGA", "EWR", "LAX", "SFO", "SAN", "ORD", "MDW", 
                 "DFW", "IAH", "ATL", "DEN", "PHX", "LAS", "SEA", "BOS",
                 "MIA", "MCO", "CLT", "PHL", "DTW", "MSP", "SLC", "PDX" -> true;
            default -> false;
        };
    }
    
    /**
     * Factory methods for creating common routes.
     */
    public static Route newYorkToLosAngeles() {
        return new Route(AirportCode.jfk(), AirportCode.lax());
    }
    
    public static Route londonToParis() {
        return new Route(AirportCode.lhr(), AirportCode.cdg());
    }
    
    public static Route newYorkToLondon() {
        return new Route(AirportCode.jfk(), AirportCode.lhr());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Route route = (Route) obj;
        return Objects.equals(origin, route.origin) && 
               Objects.equals(destination, route.destination);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(origin, destination);
    }
    
    @Override
    public String toString() {
        return String.format("Route{%s → %s}", origin, destination);
    }
}
