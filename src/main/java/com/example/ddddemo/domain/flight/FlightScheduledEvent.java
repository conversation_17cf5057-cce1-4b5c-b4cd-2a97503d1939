package com.example.ddddemo.domain.flight;

import com.example.ddddemo.domain.shared.DomainEvent;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Domain Event published when a flight is scheduled.
 * 
 * This event represents the business fact that a new flight has been
 * added to the airline's schedule. Other bounded contexts might be
 * interested in this event, such as:
 * 
 * - Pricing Context: To create initial pricing rules for the flight
 * - Customer Context: To make the flight available for booking
 * - Crew Management: To assign crew members to the flight
 * - Ground Operations: To prepare gate assignments and ground services
 * 
 * Domain events should be named in past tense because they represent
 * something that has already happened in the domain.
 */
public class FlightScheduledEvent extends DomainEvent {
    
    /**
     * The unique identifier of the scheduled flight.
     */
    private final UUID flightId;
    
    /**
     * The flight number (e.g., "AA1234").
     */
    private final String flightNumber;
    
    /**
     * The route code (e.g., "JFK-LAX").
     */
    private final String routeCode;
    
    /**
     * The scheduled departure time.
     */
    private final LocalDateTime scheduledDeparture;
    
    /**
     * Creates a new FlightScheduledEvent.
     * 
     * @param flightId The unique flight identifier
     * @param flightNumber The flight number
     * @param routeCode The route code
     * @param scheduledDeparture The scheduled departure time
     */
    public FlightScheduledEvent(UUID flightId, String flightNumber, 
                               String routeCode, LocalDateTime scheduledDeparture) {
        super();
        this.flightId = Objects.requireNonNull(flightId, "Flight ID cannot be null");
        this.flightNumber = Objects.requireNonNull(flightNumber, "Flight number cannot be null");
        this.routeCode = Objects.requireNonNull(routeCode, "Route code cannot be null");
        this.scheduledDeparture = Objects.requireNonNull(scheduledDeparture, "Scheduled departure cannot be null");
    }
    
    public UUID getFlightId() {
        return flightId;
    }
    
    public String getFlightNumber() {
        return flightNumber;
    }
    
    public String getRouteCode() {
        return routeCode;
    }
    
    public LocalDateTime getScheduledDeparture() {
        return scheduledDeparture;
    }
    
    @Override
    public String toString() {
        return String.format("FlightScheduledEvent{flightId=%s, flightNumber='%s', route='%s', departure=%s}", 
                           flightId, flightNumber, routeCode, scheduledDeparture);
    }
}
